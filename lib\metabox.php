<?php

function remove_editor_from_specific_page() {
	if (is_admin() && isset($_GET['post']) && $_GET['post'] == 2) {
		remove_post_type_support('page', 'editor');
	}
}

add_action('admin_init', 'remove_editor_from_specific_page');

/* =================================================
	//  Name        Dados para o SEO
	//  prefix 			seo
	//  function    seo_theme_metabox
==================================================*/
function seo_theme_metabox() {
    $fields = array(
        array('id' => '_yoast_wpseo_focuskw','label' => 'Meta Keywords','type' => 'text', 'class' => 'md12 sm12 xs12'),
        array('id' => '_yoast_wpseo_metadesc','label' => 'Meta descrição','type' => 'textarea', 'class' => 'md12 sm12 xs12'),
    );
    $metabox = new ThemeFieldsMetabox('Dados para o SEO', array('post','page'), $fields, 'seo', 'side','default', $postid='any');
}
add_action('admin_init', 'seo_theme_metabox');

/* =================================================
	//  Name        Introdução desta página
	//  prefix 			after_title
	//  function    after_title_theme_metabox
==================================================*/
function after_title_theme_metabox() {
	$fields = array(
		array('id' => 'intro_text','label' => '','type' => 'wpeditor', 'class' => 'md8 sm8 xs12 editor200'),
		array('id' => 'intro_img','label' => '','type' => 'media', 'class' => 'md4 sm4 xs12 after-title-img'),
		array('id' => 'palavras','label' => 'Palavras separadas por vírgula','type' => 'text', 'class' => 'md8 sm8 xs12 palavras'),

	);
	$metabox = new ThemeFieldsMetabox('<span>INTRODUÇÃO - Substitui o título da página <b style="color: darkred;">(Obrigatório ter um H2)</b></span>', array('page', 'especialidade'), $fields, 'after_title','display_after_title','core', $postid='any');
}
add_action('admin_init', 'after_title_theme_metabox');


/* =================================================
	//  Name        Sessões de conteúdo desta página
	//  prefix 			homepage
	//  function    homepage_theme_metabox
==================================================*/
function homepage_theme_metabox() {
	$fields = array(
		
		array('id' => 'voce_conhece','label' => 'Títlo da sessão','type' => 'text', 'class' => 'md9 sm9 xs12'),
		array('id' => 'img_conhece','label' => '','type' => 'media', 'class' => 'md3 sm3 xs12 img80'),
		array('id' => 'box_conhece1','label' => '','type' => 'textarea', 'class' => 'md3 sm6 xs12 editor200'),
		array('id' => 'box_conhece2','label' => '','type' => 'textarea', 'class' => 'md3 sm6 xs12 editor200'),
		array('id' => 'box_conhece3','label' => '','type' => 'textarea', 'class' => 'md3 sm6 xs12 editor200'),
		array('id' => 'box_conhece4','label' => '','type' => 'textarea', 'class' => 'md3 sm6 xs12 editor200'),
		
		array('id' => 'row','label' => ' ','type' => 'row', 'class' => 'g pt50 pb50'),
				
		array('id' => 'conheca','label' => 'Sesão conheça','type' => 'wpeditor', 'class' => 'md6 sm6 xs12 editor300'),
		
		
		array('id' => 'html','label' => ' ','type' => 'html', 'class' => '<div class="md6 sm6 xs12"><div class="g">'),
				array('id' => 'conheca_img','label' => '','type' => 'media', 'class' => 'md12 sm12 xs12 pt40 img40'),
				array('id' => 'box_conheca_img1','label' => '','type' => 'media', 'class' => 'md6 sm6 xs12 pt40 img40'),
				array('id' => 'box_conheca_img2','label' => '','type' => 'media', 'class' => 'md6 sm6 xs12 pt40 img40'),
				array('id' => 'box_conheca1','label' => '','type' => 'textarea', 'class' => 'md6 sm6 editor200 pt30'),
				array('id' => 'box_conheca2','label' => '','type' => 'textarea', 'class' => 'md6 sm6 editor200 pt30'),
		array('id' => 'html','label' => ' ','type' => 'html', 'class' => '</div></div>'),
		
		
		array('id' => 'row','label' => ' ','type' => 'row', 'class' => 'g pt50 pb50'),
		
		array('id' => 'para-que-serve','label' => 'Sesão -- Para que serve','type' => 'wpeditor', 'class' => 'md8 sm8 xs12  pb50'),
		array('id' => 'para-que-serve-img','label' => '','type' => 'media', 'class' => 'md4 sm4 xs12'),
		array('id' => 'html','label' => ' ','type' => 'html', 'class' => '<div class="xs12"><div class="g grid5">'),
				array('id' => 'faz_img1','label' => '','type' => 'media', 'class' => 'xs12 img40'),
				array('id' => 'faz_img2','label' => '','type' => 'media', 'class' => 'xs12 img40'),
				array('id' => 'faz_img3','label' => '','type' => 'media', 'class' => 'xs12 img40'),
				array('id' => 'faz_img4','label' => '','type' => 'media', 'class' => 'xs12 img40'),
				//array('id' => 'faz_img5','label' => '','type' => 'media', 'class' => 'xs12 img40'),
				array('id' => 'faz1','label' => '','type' => 'textarea', 'class' => 'xs12 editor300'),
				array('id' => 'faz2','label' => '','type' => 'textarea', 'class' => 'xs12 editor300'),
				array('id' => 'faz3','label' => '','type' => 'textarea', 'class' => 'xs12 editor300'),
				array('id' => 'faz4','label' => '','type' => 'textarea', 'class' => 'xs12 editor300'),
				//array('id' => 'faz5','label' => '','type' => 'textarea', 'class' => 'xs12 editor300'),
		array('id' => 'html','label' => ' ','type' => 'html', 'class' => '</div></div>'),
		
		
		array('id' => 'row','label' => ' ','type' => 'row', 'class' => 'g pt50 pb50'),
		
		array('id' => 'texto-valores','label' => 'Sesão Preços e valores','type' => 'wpeditor', 'class' => 'md8 sm8 xs12  pb50'),
		
		array('id' => 'row','label' => ' ','type' => 'row', 'class' => 'g pt50 pb50'),
		
		array('id' => 'cta-text1','label' => 'CTA','type' => 'wpeditor', 'class' => 'md6 sm6 xs12  pb50 editor300'),
		array('id' => 'cta-text2','label' => '&nbsp;','type' => 'wpeditor', 'class' => 'md3 sm3 xs12  pb50 editor300'),
		array('id' => 'cta-text3','label' => '&nbsp;','type' => 'wpeditor', 'class' => 'md3 sm3 xs12  pb50 editor300'),
		
	);
	$metabox = new ThemeFieldsMetabox('Sessões de conteúdo desta página', array('page'), $fields, 'homepage','advanced','default', $postid='any');
}
add_action('admin_init', 'homepage_theme_metabox');


/* =================================================
	//  Name        Outras informações sobre o plano
	//  prefix 			valores
	//  function    valores_theme_metabox
==================================================*/
function valores_theme_metabox() {
	$fields = array(
		array('id' => 'link_compra','label' => 'Link para compra','type' => 'text', 'class' => 'md4 sm4 xs12'),
		array('id' => 'valor_mensal','label' => 'Valor mensal','type' => 'text', 'class' => 'md4 sm4 xs12'),
		array('id' => 'valor_anual','label' => 'Valor anual','type' => 'text', 'class' => 'md4 sm4 xs12'),
	);
	$metabox = new ThemeFieldsMetabox('Outras informações sobre o plano', array('valor'), $fields, 'valores','advanced','default', $postid='any');
}
add_action('admin_init', 'valores_theme_metabox');

