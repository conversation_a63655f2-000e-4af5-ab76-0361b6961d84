/* ==========================================================================
// sidebar menu
========================================================================== */
.sidemenu {
	display: block !important;
	position: fixed;
	top: 0;
	bottom: 0;
	left: -101%;
	opacity: 0;
	z-index: 8;
	background-color: var(--black);
	padding: 160px 20px 80px;
	width: 100%;
	max-width: 500px;
	height: 100% !important;
	overflow: auto;
	will-change: auto;
	-webkit-transition: all 0.48s cubic-bezier(0.31, 0.62, 0.6, 0.96) 0s;
	transition: all 0.48s cubic-bezier(0.31, 0.62, 0.6, 0.96) 0s;
	color: #fff;
	a{color: #fff;&:hover{color: var(--secondary)}}
	.menu-menu-container {
		display: block;
	}
	.menu {
		display: block;
		width: 100%;
		li,
		a {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			display: block;
			position: relative;
			line-height: 30px;
		}
		>li {
			display: block;
			-webkit-transition: all 0.6s ease;
			transition: all 0.6s ease;
			>a {
				max-width: 85%;
				max-width: calc(100% - 54px);
				display: inline-block;
				font-weight: 600;
				-webkit-transition: all 0.6s ease;
				transition: all 0.6s ease;
			}
			&.menu-item-has-children::before {
				content: "";
				cursor: pointer;
				display: inline-block;
				position: absolute;
				top: 0;
				right: 0;
				opacity: .7;
				z-index: 3;
				border-left: 1px solid #000;
				background-position: 50% 50%;
				background-repeat: no-repeat;
				background-size: 18px;
				width: 35px;
				height: 28px;
				font-size: 16px;
				background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgdmlld0JveD0iMCAwIDMyIDMyIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9ImJsYWNrIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMiIgZD0iTTMwIDEyTDE2IDI0TDIgMTIiLz48L3N2Zz4=");
			}
			.sub-menu {
				display: none;
				position: relative;
				top: 0;
				left: 0px;
				margin-bottom: 10px;
				margin-left: -15px !important;
				padding: 0 0 0 20px;
				width: 95%;
				li {
					margin: 0;
					border-left: 2px solid var(--dark);
					padding: 0;
					padding-left: 10px;
					will-change: auto;
					-webkit-transition: all 0.5s;
					transition: all 0.5s;
					a {
						color: var(--black);
					}
				}
			}
			&.active .sub-menu {
				display: block;
				transform: none;
				visibility: visible;
				opacity: 1;
				-webkit-transform: none;
				-webkit-animation-name: none;
				animation-name: none;
			}
		}
	}
}
body {
	&.active-menu {
		overflow: hidden;
		.site-header {
			-webkit-box-shadow: 0px 0px 10px #00000038;
			box-shadow: 0px 0px 10px #00000038;
		}
		.open-menu {
			background: 0;
			&::before {
				transform: rotate(45deg);
			}
			&::after {
				transform: rotate(-45deg);
			}
			&::before,
			&::after {
				-webkit-transition: all .48s ease;
				transition: all .48s ease;
				top: calc(50% - 2px);
				bottom: auto;
			}
			span {
				opacity: 0
			}
		}
		.sidemenu {
			left: 0;
			visibility: visible;
			opacity: 1;
			box-shadow: 0px 0px 0px 5000px #00000080;
		}
	}
	&.load.active {
		.site-header {
			background-color: var(--black);
			box-shadow: 0px 0px 10px #00000015;
			padding-top: 10px;
			padding-bottom: 10px;
		}
		.site-branding {
			//background-color: #fff
		}
		.whatsapp-action {
			right: 70px;
		}
		.scroll-top {
			bottom: 15px;
			visibility: visible;
			opacity: 1 !important;
			&:hover {
				background-color: var(--dark);
			}
		}
	}
}
/* ==========================================================================
// footer
========================================================================== */
.site-footer {
	background-color: var(--black);
	color: var(--dark);
	margin: 0;
	padding: 0;
	a {
		color: var(--white);
		display: inline-block;
		&:hover {
			color: var(--hover)
		}
	}
	.copy {
		margin-top: 50px;
		padding: 20px 0;
		width: 100%;
		color: var(--dark);
		p {
			margin: 0;
		}
	}
	.all-right {
		display: grid;
		grid-template-columns: auto 40px;
		align-items: center;
	}
}
a.flex-contato, .flex-contato {
	display: grid;
	grid-template-columns: 30px auto;
	column-gap: 15px;
	width: 100%;
}

a#dev {
	opacity: 0;
}
/* ==========================================================================
// Cookie Consent
========================================================================== */
.cookieConsentContainer {
	box-sizing: border-box;
	display: block;
	position: fixed;
	right: 20px;
	bottom: 20px;
	opacity: 1 !important;
	z-index: 999;
	background: var(--white);
	padding: 20px;
	width: 300px;
	min-height: 20px;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	.cookieTitle {
		color: var(--primary);
		font-size: 18px;
		line-height: 18px;
		display: block;
	}
	.cookieDesc {
		p {
			display: block;
			margin: 10px 0px 0px;
			padding: 0px;
			line-height: 15px;
			letter-spacing: -0.2px;
			color: var(--dark);
			font-size: 12px;
			a {
				display: inline-block;
				color: var(--dark);
			}
		}
		a {
			color: var(--white);
			text-decoration: underline;
		}
	}
	.cookieButton a {
		display: block;
		margin-top: 14px;
		background-color: var(--primary);
		padding: 5px 24px;
		text-align: center;
		color: var(--white);
		font-size: 14px;
		font-weight: bold;
		-webkit-transition: background 0.3s ease 0s;
		transition: background 0.3s ease 0s;
		&:hover {
			cursor: pointer;
			background-color: var(--dark);
		}
	}
	&.hidden {
		display: none;
	}
}
.home:not(.active) {
	cookieConsentContainer {
		display: none;
	}
}
/* ==========================================================================
// Whatsapp btn
========================================================================== */
.whatsapp-action {
	display: inline-block;
	position: fixed;
	right: 15px;
	bottom: 15px;
	z-index: 9;
	z-index: 102;
	border-radius: 4px;
	background: #26d366;
	width: 45px;
	height: 45px;
	text-align: center;
	line-height: 45px;
	font-weight: 300;
	-webkit-transition: all 0.5s;
	transition: all 0.5s;
	span {
		position: absolute;
		top: 3px;
		left: 0;
		width: 100%;
		height: 100%;
	}
}
/* ==========================================================================
// Scroll to top
========================================================================== */
.scroll-top {
	display: inline-block;
	position: fixed;
	right: 15px;
	bottom: 0;
	visibility: hidden;
	opacity: 0;
	z-index: 12;
	border-radius: 4px;
	background-color: var(--primary);
	padding: 0;
	width: 45px;
	height: 45px;
	text-align: center;
	line-height: 42px;
	font-weight: 700;
	-webkit-transition: all 0.5s;
	transition: all 0.5s;
	svg {
		display: inline-block;
		margin-top: -5px;
	}
}
/* ==========================================================================
// mail message
========================================================================== */
.mail-message {
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
	z-index: 999999;
	box-shadow: 0px 0px 0px 5000px #000000d4;
	background: #00ad9a;
	padding: 30px 10px;
	text-align: center;
	color: var(--white);
	&.error {
		background: #dc5656
	}
}