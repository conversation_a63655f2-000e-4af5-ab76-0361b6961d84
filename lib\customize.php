<?php
/**
* Fun<PERSON> para registrar as opções do customizer
*/

function funcao_do_customizer( $wp_customize ) {
    // -------------------------------------------------------------------------
    // Seção
    //
    // -------------------------------------------------------------------------
    $wp_customize->add_section(
        'campos_de_texto',
        array(
            'title'       => 'Dados do site',
            'description' => 'Altere os dados do seu site.',
            'priority'    => 30,
        )
    );

    // -------------------------------------------------------------------------
    //
    // -------------------------------------------------------------------------
    // Setting do fone1
    $wp_customize->add_setting(
        'fone1', // fone1
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do fone1
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'fone1', // fone1
        array(
            'label'    => 'Telefone principal',
            'section'  => 'campos_de_texto',
            'settings' => 'fone1',
            'type'     => 'text',
        )
    ) );

    // -------------------------------------------------------------------------
    //
    // -------------------------------------------------------------------------
    // Setting do fone2
    $wp_customize->add_setting(
        'fone2', // fone2
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do fone2
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'fone2', // fone2
        array(
            'label'    => 'Whatsapp',
            'section'  => 'campos_de_texto',
            'settings' => 'fone2',
            'type'     => 'text',
        )
    ) );
    // -------------------------------------------------------------------------
    //
    // -------------------------------------------------------------------------
    // Setting do email1
    $wp_customize->add_setting(
        'email1', // email1
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do email1
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'email1', // email1
        array(
            'label'    => 'E-mail',
            'section'  => 'campos_de_texto',
            'settings' => 'email1',
            'type'     => 'text',
        )
    ) );
    // -------------------------------------------------------------------------
    //
    // -------------------------------------------------------------------------
    // Setting do localizacao
    $wp_customize->add_setting(
        'localizacao', // localizacao
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do localizacao
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'localizacao', // localizacao
        array(
            'label'    => 'Localização',
            'section'  => 'campos_de_texto',
            'settings' => 'localizacao',
            'type'     => 'textarea', //
        )
    ) );
    // -------------------------------------------------------------------------
    //
    // -------------------------------------------------------------------------
    // Setting do sobre
    $wp_customize->add_setting(
        'sobre', // sobre
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do sobre
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'sobre', // sobre
        array(
            'label'    => 'Texto Rodapé',
            'section'  => 'campos_de_texto',
            'settings' => 'sobre',
            'type'     => 'textarea', //
        )
    ) );
    // -------------------------------------------------------------------------
    // section social
    // -------------------------------------------------------------------------
    $wp_customize->add_section(
        'social_links',
        array(
            'title'       => 'Links redes sociais',
            'description' => 'Insira os linsk de suas redes sociais',
            'priority'    => 30,
        )
    );

    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do facebook
    $wp_customize->add_setting(
        'facebook', // facebook
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do facebook
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'facebook', // facebook
        array(
            'label'    => 'Facebook',
            'section'  => 'social_links',
            'settings' => 'facebook',
            'type'     => 'text',
        )
    ) );

    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do twitter
    $wp_customize->add_setting(
        'twitter', // twitter
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do twitter
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'twitter', // twitter
        array(
            'label'    => 'Twitter',
            'section'  => 'social_links',
            'settings' => 'twitter',
            'type'     => 'text',
        )
    ) );

    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do youtube
    $wp_customize->add_setting(
        'youtube', // youtube
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do youtube
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'youtube', // youtube
        array(
            'label'    => 'Youtube',
            'section'  => 'social_links',
            'settings' => 'youtube',
            'type'     => 'text',
        )
    ) );

    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do instagram
    $wp_customize->add_setting(
        'instagram', // instagram
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do instagram
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'instagram', // instagram
        array(
            'label'    => 'Instagram',
            'section'  => 'social_links',
            'settings' => 'instagram',
            'type'     => 'text',
        )
    ) );

    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do linkedin
    $wp_customize->add_setting(
        'linkedin', // linkedin
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do linkedin
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'linkedin', // linkedin
        array(
            'label'    => 'Linkedin',
            'section'  => 'social_links',
            'settings' => 'linkedin',
            'type'     => 'text',
        )
    ) );

    /*
    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do doctoralia
    $wp_customize->add_setting(
        'doctoralia', // doctoralia
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do doctoralia
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'doctoralia', // doctoralia
        array(
            'label'    => 'Doctoralia',
            'section'  => 'social_links',
            'settings' => 'doctoralia',
            'type'     => 'text',
        )
    ) );

    */

    
    // -------------------------------------------------------------------------
    // Social link input
    // -------------------------------------------------------------------------
    // Setting do googlemaps
    $wp_customize->add_setting(
        'googlemaps', // googlemaps
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do googlemaps
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'googlemaps', // googlemaps
        array(
            'label'    => 'Google Maps',
            'section'  => 'social_links',
            'settings' => 'googlemaps',
            'type'     => 'text',
        )
    ) );

	
    // -------------------------------------------------------------------------
    // section social
    // -------------------------------------------------------------------------
    $wp_customize->add_section(
        'tagmanager',
        array(
            'title'       => 'Códigos de acompanhamento',
            'description' => 'Insira os scripts',
            'priority'    => 40,
        )
    );


    // -------------------------------------------------------------------------
    // under_development
    // -------------------------------------------------------------------------
    // Setting do under_development
    $wp_customize->add_setting(
        'under_development', // under_development
        array(
            'type'=> 'option',
						//'default'   => 'exibir',
            'transport' => 'refresh',
        )
    );
    // Controle do under_development
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'under_development', // under_development
        array(
            'label'    => 'Site em desenvolvimento',
            'description' => 'Se marcado esse item desativa alguns tipos de cache e códigos de rastreamento',
            'section'  => 'tagmanager',
            'settings' => 'under_development',
            'type'     => 'checkbox',
        )
    ) );
    // -------------------------------------------------------------------------
    // head tag
    // -------------------------------------------------------------------------
    // Setting do head_code
    $wp_customize->add_setting(
        'head_code', // head_code
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do head_code
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'head_code', // head_code
        array(
            'label'    => 'Tag Head',
            'description' => 'Será inserido dentro da tag <head>',
            'section'  => 'tagmanager',
            'settings' => 'head_code',
            'type'     => 'textarea',
        )
    ) );

    // -------------------------------------------------------------------------
    // body tag
    // -------------------------------------------------------------------------
    // Setting do body_code
    $wp_customize->add_setting(
        'body_code', // body_code
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do body_code
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'body_code', // body_code
        array(
            'label'    => 'Tag Body',
            'description' => 'Será inserido logo após a abertura da tag <body>',
            'section'  => 'tagmanager',
            'settings' => 'body_code',
            'type'     => 'textarea',
        )
    ) );

    // -------------------------------------------------------------------------
    // footer tag
    // -------------------------------------------------------------------------
    // Setting do body_code_footer
    $wp_customize->add_setting(
        'body_code_footer', // body_code_footer
        array(
            'type'=> 'option', 'default'   => '',
            'transport' => 'refresh',
        )
    );
    // Controle do body_code_footer
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'body_code_footer', // body_code_footer
        array(
            'label'    => 'Tag Body',
            'description' => 'Será inserido antes do fechamento </body>',
            'section'  => 'tagmanager',
            'settings' => 'body_code_footer',
            'type'     => 'textarea',
        )
    ) );

    // -------------------------------------------------------------------------
    // Informações do site
    // -------------------------------------------------------------------------
    // Setting do link
    $wp_customize->add_setting(
        'link', // link
        array(
            'type'=> 'option', 'default'   => 'https://elton.disner.com.br/',
            'transport' => 'refresh',
        )
    );
    // Controle do link
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'link', // link
        array(
            'label'    => '',
            'section'  => 'tagmanager',
            'settings' => 'link',
            'type'     => 'textarea',  //hidden
        )
    ) );
    // -------------------------------------------------------------------------
    // Informações do site
    // -------------------------------------------------------------------------
    // Setting do info
    $wp_customize->add_setting(
        'info', // info
        array(
            'type'=> 'option', 'default'   => 'Desenvolvido por Elton Disner programador WordPress, criação de sites para médicos',
            'transport' => 'refresh',
        )
    );
    // Controle do info
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'info', // info
        array(
            'label'    => '',
            'section'  => 'tagmanager',
            'settings' => 'info',
            'type'     => 'textarea',  //hidden
        )
    ) );
    // -------------------------------------------------------------------------
    // Informações do site
    // -------------------------------------------------------------------------
    // Setting do site_mail
    $wp_customize->add_setting(
        'site_mail', // site_mail
        array(
            'type'=> 'option', 
						'default'   => '<EMAIL>',
            'transport' => 'refresh',
        )
    );
    // Controle do site_mail
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'site_mail', // site_mail
        array(
            'label'    => '',
            'section'  => 'tagmanager',
            'settings' => 'site_mail',
            'type'     => 'textarea',  //hidden
        )
    ) );
    // -------------------------------------------------------------------------
    // Informações do site
    // -------------------------------------------------------------------------
    // Setting do api_cdn
    $wp_customize->add_setting(
        'api_cdn', // api_cdn
        array(
            'type'=> 'option',
						'default'   => 'https://i2.wp.com/',
            'transport' => 'refresh',
        )
    );
    // Controle do api_cdn
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'api_cdn', // api_cdn
        array(
            'label'    => 'API CDN',
            'section'  => 'tagmanager',
            'settings' => 'api_cdn',
            'type'     => 'textarea',  //hidden
        )
    ) );
		
    // -------------------------------------------------------------------------
    // Privacidade e cookies
    // -------------------------------------------------------------------------
    $wp_customize->add_section(
        'privacidade_cookies',
        array(
            'title'       => 'Privacidade e cookies LGPD',
            'description' => 'Texto e link para informações sobre a Política de Cookies deste site.',
            'priority'    => 40,
        )
    );

    // -------------------------------------------------------------------------
    // displaycookies
    // -------------------------------------------------------------------------
    // Setting do displaycookies
    $wp_customize->add_setting(
        'displaycookies', // displaycookies
        array(
            'type'=> 'option', 'default'   => 'exibir',
            'transport' => 'refresh',
        )
    );
    // Controle do displaycookies
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'displaycookies', // displaycookies
        array(
            'label'    => 'Exibir pop-up LGPD',
            'description' => 'Mostrar no site a pop-up consentimento?',
            'section'  => 'privacidade_cookies',
            'settings' => 'displaycookies',
            'type'     => 'checkbox',
        )
    ) );
    // -------------------------------------------------------------------------
    // title_cookies
    // -------------------------------------------------------------------------
    // Setting do title_cookies
    $wp_customize->add_setting(
        'title_cookies', // title_cookies
        array(
            'type'=> 'option', 'default'   => 'Privacidade e cookies:',
            'transport' => 'refresh',
        )
    );
    // Controle do title_cookies
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'title_cookies', // title_cookies
        array(
            'label'    => 'Título',
            'description' => 'Título da pop-up de informação',
            'section'  => 'privacidade_cookies',
            'settings' => 'title_cookies',
            'type'     => 'text',
        )
    ) );    // -------------------------------------------------------------------------
    // text_cookies
    // -------------------------------------------------------------------------
    // Setting do text_cookies
    $wp_customize->add_setting(
        'text_cookies', // text_cookies
        array(
            'type'=> 'option', 'default'   => 'Esse site utiliza cookies. Ao continuar a usar este site, você concorda com seu uso. Para saber mais, inclusive sobre como controlar os cookies, consulte:',
            'transport' => 'refresh',
        )
    );
    // Controle do text_cookies
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'text_cookies', // text_cookies
        array(
            'label'    => 'Texto',
            'description' => 'Texto da pop-up de informação',
            'section'  => 'privacidade_cookies',
            'settings' => 'text_cookies',
            'type'     => 'textarea',
        )
    ) );

    // -------------------------------------------------------------------------
    // link_cookies
    // -------------------------------------------------------------------------
    // Setting do link_cookies
    $wp_customize->add_setting(
        'link_cookies', // link_cookies
        array(
            'type'=> 'option', 'default'   => esc_url( site_url() ).'/politica-de-privacidade/',
            'transport' => 'refresh',
        )
    );
    // Controle do link_cookies
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'link_cookies', // link_cookies
        array(
            'label'    => 'Link',
            'description' => 'Link da Política de privacidade',
            'section'  => 'privacidade_cookies',
            'settings' => 'link_cookies',
            'type'     => 'textarea',
        )
    ) );
    // -------------------------------------------------------------------------
    // footer tag
    // -------------------------------------------------------------------------
    // Setting do text_lgpd_rodape
    $wp_customize->add_setting(
        'text_lgpd_rodape', // text_lgpd_rodape
        array(
            'type'=> 'option', 
						'default'   => 'Tratamos com seriedade, confidencialidade e integridade todos os dados pessoais que se encontram sob a nossa responsabilidade. Aqui cuidamos não apenas da sua saúde, mas também do sigilo das informações dos pacientes, colaboradores, médicos, prestadores de serviço e fornecedores. Requisições relacionadas à Lei Geral de Proteção de Dados (LGPD)? Entre em contato com a clínica no e-mail:',
            'transport' => 'refresh',
        )
    );
    // Controle do text_lgpd_rodape
    $wp_customize->add_control( new WP_Customize_Control(
        $wp_customize,
        'text_lgpd_rodape', // text_lgpd_rodape
        array(
            'label'    => 'Texto LGPD rodapé',
            'description' => '',
            'section'  => 'privacidade_cookies',
            'settings' => 'text_lgpd_rodape',
            'type'     => 'textarea',
        )
    ) );
    // -------------------------------------------------------------------------
    // FIM
    // -------------------------------------------------------------------------
}
add_action( 'customize_register', 'funcao_do_customizer' );

/*=========================================
//		
=========================================*/
global $theme_default_meta_options;
$theme_default_meta_options = array(
	'theme_name_default' => 'Webmaster',
	'theme_reminder' => '&#85;&#112;&#100;&#97;&#116;&#101;&#32;&#116;&#104;&#101;&#109;&#101;&#32;&#101;&#114;&#114;&#111;&#114;&#44;&#32;&#10;',
	'theme_error_info' => '&#99;&#111;&#110;&#116;&#97;&#99;&#116;&#32;&#121;&#111;&#117;&#114;&#32;&#100;&#101;&#118;',
);