<?php 

/*=========================================
//		Adiciona classes customizadas à tag <body> no WordPress.
=========================================*/
function slug_body_class($classes) {
    global $post; 
    if (isset($post)) {
        $bodyclass_meta = get_post_meta(get_the_ID(), 'bodyclass', true);
        if (!empty($bodyclass_meta)) {
            $classes[] = $bodyclass_meta;
        }
        $classes[] = $post->post_type; 
        $classes[] = $post->post_name; 
    }
    return $classes;
}
add_filter('body_class', 'slug_body_class');

/*=========================================
//		styles and scripts
=========================================*/
function theme_wp_enqueue() {
    wp_enqueue_style('default_style', get_template_directory_uri() . '/css/default_style.css', array(), WP_VERSION);

    wp_enqueue_script('default_script', get_template_directory_uri() . '/js/default_script.js', array(), WP_VERSION, true);
}
add_action('wp_enqueue_scripts', 'theme_wp_enqueue');


add_filter('style_loader_tag', 'theme_remove_type_attr', 100, 2);
add_filter('script_loader_tag', 'theme_remove_type_attr', 100, 2);
function theme_remove_type_attr($tag, $handle) {
    return preg_replace("/type=['\"]text\/(javascript|css)['\"]/", '', $tag);
}

/* =================================================
//	theme metas
==================================================*/
add_action('wp_head', 'theme_metas', 5);

function theme_metas() {
    if (!is_user_logged_in()) {
        echo theme_preload_fonts();
    }
        echo theme_preload_resources();
        echo theme_og_social_metas();
}

function theme_preload_fonts() {
    $fonts = [
        '/fonts/poppins-v23-latin-regular.woff2',
        '/fonts/poppins-v23-latin-300.woff2',
        '/fonts/poppins-v23-latin-600.woff2'
    ];
    $output = '';
    foreach ($fonts as $font) {
        $output .= "<link rel='preload' fetchpriority='high' href='" . THEME_DIR . "$font' as='font' type='font/woff2' crossorigin='anonymous'>\n";
    }
    return $output;
}

function theme_preload_resources() {

		$output = "<meta content='width=device-width,initial-scale=1.0' name='viewport'>\n";
		$output .= "<meta content='IE=edge' http-equiv='X-UA-Compatible'>\n";
		$output .= "<meta content='chrome=1' http-equiv='X-UA-Compatible'>\n";
		$output .= "<meta content='origin' name='referrer'>\n";
		$output .= "<meta name=author content='Elton Disner'>\n";
		$output .= "<meta name=designer content='Elton Disner'>\n";
		$output .= "<link rel='dns-prefetch' href='//i2.wp.com'>\n";
		$output .= "<link href='https://i2.wp.com' rel='preconnect' crossorigin>\n";
		$output .= get_site_option('site_preconnect');
    $output .= "<link rel='preload' fetchpriority='high' href='" . THEME_DIR . "/css/".get_site_option('style_name', 'default_style').".css' as='style' importance='high'>\n";
    $output .= "<link rel='preload' fetchpriority='high' href='" . THEME_DIR . "/js/".get_site_option('script_name', 'default_script').".js' as='script' importance='high'>\n";
    $output .= "<meta property='og:locale' content='pt_BR' />\n";
    $output .= "<meta name='msapplication-TileColor' content='#000'>\n";
    $output .= "<meta name='theme-color' content='#000'>\n";
		$output .= "<meta name='referrer' content='always'>\n";
		$output .= "<meta name='mobile-web-app-capable' content='yes'>\n";
		$output .= "<meta name='apple-mobile-web-app-capable' content='yes'>\n";
		$output .= "<meta http-equiv='Accept-CH' content='Device-Memory'>\n";
		$output .= "<meta name='copyright' content='ConsulFis'>\n";
		$output .= "<meta name='format-detection' content='telephone=no'>\n";
    $output .= "<meta name='robots' content='index, follow' />\n";
	
    if (get_site_option('under_development') == 1) {
        $output .= "<meta http-equiv='Pragma' content='no-cache'>\n";
        $output .= "<meta http-equiv='Expires' content='0'>\n";
    }
    return $output;
}


function theme_og_social_metas() {
    $output = '';
    if (has_post_thumbnail()) {
        $t = wp_get_attachment_image_src(get_post_thumbnail_id(get_the_ID()), 'post');
        $image = $t[0];
        $width = $t[2];
        $height = $t[1];
    } else {
        $image = THEME_DIR . '/images/default.jpg';
        $width = $height = '';
    }
    $output .= "<meta property='og:image' itemprop='image' content='$image' />\n";
    $output .= "<meta property='og:image:url' itemprop='image' content='$image' />\n";
    $output .= "<meta name='twitter:image' itemprop='image' content='$image' />\n";
    $output .= "<meta property='og:image:secure_url' content='$image' />\n";
    $output .= "<meta property='og:url' content='" . get_the_permalink() . "' />\n";
    $output .= "<meta property='og:image:width' content='$width' />\n";
    $output .= "<meta property='og:image:height' content='$height' />\n";
    $output .= "<meta property='og:image:alt' content='" . get_the_title() . "' />\n";
    $output .= "<meta name='twitter:card' content='summary_large_image' />\n";
    $output .= "<meta property='og:site_name' content='" . get_bloginfo('name') . "' />\n";
    $output .= "<meta name='twitter:title' content='" . get_the_title() . " - " . get_bloginfo('name') . "' />\n";
    $output .= "<meta property='og:title' content='" . get_the_title() . " - " . get_bloginfo('name') . "' />\n";
    $output .= "<meta name='format-detection' content='telephone=no'>\n";
    $output .= "<meta name='robots' content='index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1'>\n";

    if (is_front_page()) {
        $description = get_post_meta(get_the_ID(), '_yoast_wpseo_metadesc', true) ?: get_bloginfo('description', 'display');
    } else {
        $description = get_post_meta(get_the_ID(), '_yoast_wpseo_metadesc', true) ?: wp_strip_all_tags(get_the_excerpt());
    }
    $output .= "<meta name='description' content='$description'>\n";
    $output .= "<meta property='og:description' content='$description' />\n";
    $output .= "<meta name='twitter:description' content='$description' />\n";

    if (is_singular()) {
        $output .= "<link rel='canonical' href='" . get_the_permalink() . "' />\n";
    }

    return $output;
}

/*=========================================
//		
=========================================*/	
function bg_id() {
    $post_id = get_the_ID();

    function get_bg_image_url($post_id) {
        if (is_front_page() || get_post_meta($post_id, 'intro_img', true)) {
            return get_post_meta($post_id, 'intro_img', true);
        } elseif (is_home() || is_archive()) {
            $blog_page_id = get_option('page_for_posts'); 
            return get_post_meta($blog_page_id, 'intro_img', true);
        } elseif (has_post_thumbnail($post_id)) {
            return wp_get_attachment_image_url(get_post_thumbnail_id($post_id), 'full');
        } else {
            $front_page_id = get_option('page_on_front');
            return wp_get_attachment_image_url(get_post_thumbnail_id($front_page_id), 'full');
        }
    }
    
    $thumb = get_bg_image_url($post_id);
    $thumbm = get_post_meta($post_id, 'intro_img_mobile', true) ? get_post_meta($post_id, 'intro_img_mobile', true) : $thumb;

    $thumb = wp_cdn($thumb);
    $thumbm = wp_cdn($thumbm);
    $thumb_escaped = esc_url($thumb);
    $thumbm_escaped = esc_url($thumbm);
    
    echo '<link media="(min-width: 768px)" rel="preload" as="image" href="' . $thumbm_escaped . '?w=1920&ssl=1">';
    echo '<link media="(max-width: 767px)" rel="preload" as="image" href="' . $thumb_escaped . '?resize=480,600&ssl=1">';
    
    $custom_css = "@media(min-width:768px){#bg{background-image:url('" . $thumb_escaped . "?w=1920&ssl=1');}}@media(max-width:767px){#bg{background-image:url('" . $thumbm_escaped . "?resize=480,600&ssl=1');}}";
    echo '<style>' . $custom_css . '</style>';

}

add_action('wp_head', 'bg_id', 5);
/*=========================================
//		
=========================================*/
add_action('wp_head', 'display_custom_head_code');
function display_custom_head_code() {
		echo '<script>var T="'.get_template_directory_uri().'"</script>';
	
    $head_code = get_site_option('head_code');
    if (!empty($head_code) && get_site_option('under_development') !== '1') {
        echo $head_code;
    }
}

/*=========================================
//		before body
=========================================*/
function before_body() {
    theme_email();
		$head_code = get_site_option('body_code');
    if (!empty($head_code) && get_site_option('under_development') !== '1') {echo $head_code;}
}

/*=========================================
//		
=========================================*/
add_action('wp_footer', 'display_custom_footer_code');
function display_custom_footer_code() {
    $footer_code = get_option('body_code_footer');

    if (!empty($footer_code) && get_option('under_development') !== '1') {
        echo $footer_code;
    }
}

/*=========================================
//		
=========================================*/
function remove_site_name_from_title($title, $sep, $seplocation) {
    if (is_single()) {
        $title = get_the_title();
    }
    return $title;
}
add_filter('wp_title', 'remove_site_name_from_title', 10, 3);
function remove_site_name_from_document_title($title) {
    if (is_single()) {
        $title = get_the_title();
    }
    return $title;
}
add_filter('pre_get_document_title', 'remove_site_name_from_document_title', 10, 1);
/*=========================================
//		
=========================================*/
function get_page_title() {
    $page_titles = [
        'category' => function() {
            echo '<h1 itemprop="headline">' . single_cat_title('', false) . '</h1>';
            echo '<div class="description">' . category_description() . '</div>';
        },
        'tax' => function() {
            echo '<h1 itemprop="headline">' . single_term_title('', false) . '</h1>';
        },
        'tag' => function() {
            echo '<h1 itemprop="headline">' . single_tag_title('', false) . '</h1>';
        },
        'page' => function() {
            echo '<h1 itemprop="headline">' . get_the_title() . '</h1>';
        },
        'single' => function() {
            echo '<h1 itemprop="headline">' . get_the_title() . '</h1>';
        },
        'post_type_archive' => function() {
            echo '<h1 itemprop="headline">'; post_type_archive_title(); echo '</h1>';
        },
        'search' => function() {
            echo '<h1 itemprop="headline">Pesquisa</h1>';
        },
        '404' => function() {
            echo '<h1 itemprop="headline">Erro 404 - Página não encontrada</h1>';
        },
        'home' => function() {
            echo '<h1 itemprop="headline">'.get_the_title(get_option( 'page_for_posts' )).'</h1>';
        },
        'day' => function() {
            echo sprintf(__('Daily Archives: <span>%s</span>', THEME_NS), get_the_date());
        },
        'month' => function() {
            echo sprintf(__('Monthly Archives: <span>%s</span>', THEME_NS), get_the_date('F Y'));
        },
        'year' => function() {
            echo sprintf(__('Yearly Archives: <span>%s</span>', THEME_NS), get_the_date('Y'));
        },
        'author' => function() {
            the_post();
            if (wp_get_attachment_image(get_user_meta(get_the_author_meta('ID'), 'user_attachment_id', true))) {
                echo wp_get_attachment_image(get_user_meta(get_the_author_meta('ID'), 'user_attachment_id', true), array('80', '80'), '', array("class" => "avatar"));
            } else {
                echo get_avatar(get_the_author_meta(get_the_ID()));
            }
            echo '<h1 itemprop="headline">' . get_the_author() . '</h1>';
            $desc = get_the_author_meta('description');
            if ($desc) {
                echo '<div class="author-description">' . $desc . '</div>';
            }
            rewind_posts();
        },
        'paged' => function() {
            echo '<h1 itemprop="headline">Blog</h1>';
        }
    ];

    foreach ($page_titles as $condition => $callback) {
        if (call_user_func('is_' . $condition)) {
            call_user_func($callback);
            break;
        }
    }
}

/*=========================================
//		content excerpt
=========================================*/
function theme_get_excerpt($post_id = null, $limit = 18) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    $post = get_post($post_id);
    if (!$post) {
        return ''; 
    }
    if (has_excerpt($post_id)) {
        $excerpt = wp_strip_all_tags($post->post_excerpt, true);
    } else {
        $content = strip_shortcodes($post->post_content);
        $content = wpautop($content);
        preg_match('/<p>(.*?)<\/p>/s', $content, $match);
        if (isset($match[0])) {
            $text = strip_tags($match[0]);
            $excerpt = wp_trim_words($text, $limit);
        } else {
            $excerpt = wp_trim_words(wp_strip_all_tags($content), $limit);
        }
    }
    return $excerpt;
}

/*=========================================
//		Posts navigation
=========================================*/
function theme_page_navigation() {
    global $wp_query;
    $big = 999999999;
    $args = array('base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))), 'format' => '?page=%#%', 'total' => $wp_query->max_num_pages, 'current' => max(1, get_query_var('paged')), 'show_all' => false, 'end_size' => 3, 'mid_size' => 2, 'prev_next' => True, 'prev_text' => __('&laquo; Previous'), 'next_text' => __('Next &raquo;'), 'type' => 'list',);
    echo '<div class="e-pager">' . paginate_links($args) . '</div> ';
}
/*=========================================
//		
=========================================*/	
function theme_stylize_pagination($pagination) {
    if ($pagination) {
        $pagination = '<div class="theme-pager">' . str_replace(array('current', 'dots'), array('current active', 'dots more'), $pagination) . '</div>';
    }
    return $pagination;
}

/*=========================================
//		Minify 
=========================================*/
function theme_minify_start() {
  if (!is_user_logged_in() && get_site_option('under_development') != 1) {
    function sanitize_output($buffer) {
        $replace = array(
        //'~method="post"~' => '',        // CUSTOM THEME
         //'~<form~' => '<aside',         // CUSTOM THEME
         //'~<(/)?form>~' => '</aside>',  // CUSTOM THEME
				'/\/\*[^\/]*\*\//' => '',
        '/\/\*\*((\r\n|\n) \*[^\n]*)+(\r\n|\n) \*\//' => '',
        '/\n(\s+)?\/\/[^\n]*/' => '',
        '/([\n])+/' => "$1",
				'|\s\s+|' => ' ',
        '/\>[^\S ]+/s' => '>',
        '/[^\S ]+\</s' => '<',
         '/([\t ])+/s' => ' ',
        '/^([\t ])+/m' => '',
        '/([\t ])+$/m' => '',
        '~//[a-zA-Z0-9 ]+$~m' => '',
        '/[\r\n]+([\t ]?[\r\n]+)+/s' => "\n",
				'/\>[\r\n\t ]+\</s' => '><',
        '/}[\r\n\t ]+/s' => '}',
        '/}[\r\n\t ]+,[\r\n\t ]+/s' => '},',
        '/\)[\r\n\t ]?{[\r\n\t ]+/s' => '){',
        '/,[\r\n\t ]?{[\r\n\t ]+/s' => ',{',
        '/\),[\r\n\t ]+/s' => '),',
        '~([\r\n\t ])?([a-zA-Z0-9]+)="([a-zA-Z0-9_/\\-]+)"([\r\n\t ])?~s' => '$1$2=$3$4',
        );
        $buffer = preg_replace(array_keys($replace), array_values($replace), $buffer);
        return $buffer;
    }
    ob_start("sanitize_output");
  }
}

/*=========================================
//		url youtube to iframe
=========================================*/	
function get_video($string) {
		return preg_replace(
        "/\s*[a-zA-Z\/\/:\.]*youtu(be.com\/watch\?v=|.be\/)([a-zA-Z0-9\-_]+)([a-zA-Z0-9\/\*\-\_\?\&\;\%\=\.]*)/i",
        "<div class=\"bg video-container playtube\" data-embed=\"$2\"  data-bg=\"https://img.youtube.com/vi/$2/maxresdefault.jpg\"><span class=play></span></div>",$string);
}

/*=========================================
//		filter content
=========================================*/
function filter_content($content) {
	
		$content = preg_replace('/ data-sourcepos="[^"]*"/', '', $content);
		$content=strip_tags($content, '<div>,<p>,<a>,<b>,<strong>,<h1>,<h2>,<h3>,<h4>,<h5>,<h6>,<blockquote>,<br>,<hr>,<em>,<i>,<ul>,<ol>,<li>,<picture>,<embed>,<iframe>,<img>,<video>,<audio>');
    $pattern = '~<iframe.*</iframe>|<embed.*</embed>~';
    preg_match_all($pattern, $content, $matches);
	
    foreach ($matches[0] as $match) {
        $wrappedframe = '<div class="video-container">' . $match . '</div>';
        $content = str_replace($match, $wrappedframe, $content);
    }
	
	$str = [
		'alt=""',
		'fetchpriority="high"',
	];
	$rplc =[
		'alt="'.get_the_title().'"', 
		'loading="lazy" fetchpriority="low"'
	];

	
	return str_replace($str,$rplc,$content);
}
add_filter('the_content', 'filter_content');

/*=========================================
//		
=========================================*/
function convert_caption_to_figure($content) {
    $pattern = '/\[caption id="(attachment_\d+)" align="(\w+)" width="(\d+)"\](.*?)\[\/caption\]/i';

    $content = preg_replace_callback($pattern, function ($matches) {
        $attachment_id = $matches[1];
        $inner_content = $matches[4];
        
        preg_match('/<img src="([^"]+)" alt="([^"]+)" width="(\d+)" height="(\d+)" data-class="([^"]+)" \/>/', $inner_content, $img_matches);
        $image_url = wp_cdn($img_matches[1]);
        $alt_text = $img_matches[2];
        $image_width = $img_matches[3];
        $image_height = $img_matches[4];
        
        $caption = strip_tags(str_replace($img_matches[0], '', $inner_content));

        $figure_html = '<figure class="wp-caption alignnone mt10 mb20">';
        $figure_html .= '<picture>';
        $figure_html .= '<source srcset="' . $image_url . '?w=480&ssl=1" media="(max-width: 600px)">';
        $figure_html .= '<source srcset="' . $image_url . '?w=1170&ssl=1" media="(min-width: 601px)">';
        $figure_html .= '<img decoding="async" loading="lazy" fetchpriority="low" src="' . $image_url . '?w=480&ssl=1" alt="' . esc_attr($alt_text) . '" width="' . $image_width . '" height="' . $image_height . '">';
        $figure_html .= '</picture>';
        if (!empty($caption)) {
            $figure_html .= '<figcaption class="wp-caption-text">' . esc_html($caption) . '</figcaption>';
        }
        $figure_html .= '</figure>';
        
        return $figure_html;
    }, $content);

    return $content;
}

add_filter('the_content', 'convert_caption_to_figure');

/* =================================================
// 	share links
//	fb,twitter,whatsapp,telegram,line,skype,pinterest,email,viber,mailru,weibo,viber,vk,tumblr,linkedin,reddit
==================================================*/
function social($class = '', $icons = 'facebook,instagram,youtube,tiktok,twitter,linkedin') {
    $icons = array_map('trim', explode(",", $icons));
    $output = '<ul class="inline ' . $class . '">';

    foreach ($icons as $icon) {
        $siteOption = get_site_option($icon);
        
        if ($siteOption) {
            $output .= '<li><a href="' . esc_url($siteOption) . '" rel="noopener" aria-label="ver '.$icon.' '.get_bloginfo('name') . '" target="_blank"><span class="icon icon-'.$icon.'"></span></a></li>';
        }
    }

    $output .= '</ul>';

    echo $output;
}

/* =================================================
// share links
==================================================*/
function share($icons = 'facebook,twitter,whatsapp,telegram,linkedin,email') {
    $icons = explode(",", str_replace(' ', '', $icons));
    $output = '';

    foreach ($icons as $icon) {
        $output .= '<button class="social_share" data-url="' . get_permalink() . '" data-type="' . $icon . '" aria-label="Compartilhar no ' . $icon . '"><span class="icon '.$icon.'"></span></button>';
    }

    echo $output;
}

/* =================================================
//  Phone cleaner
==================================================*/
function antispam($str) {
    $str = str_replace('(', '', $str);
    $str = str_replace(')', '', $str);
    $str = str_replace(' ', '', $str);
    $str = str_replace('-', '', $str);
    $return = '';
    for ($i = 0;$i < strlen($str);$i++) {
        $return.= '&#x' . bin2hex(substr($str, $i, 1)) . ';';
    }
    return $return;
}

/*=========================================
//		
=========================================*/
function theme_post_bg_thumbnail_url($class = '') {
    $thumbnail_url = get_the_post_thumbnail_url();

    if (empty($thumbnail_url)) {
        $thumbnail_url = THEME_DIR . '/images/default.jpg';
    }

    echo '<div class="' . esc_attr($class) . ' bg" data-bg="' . esc_url($thumbnail_url) . '"><a href="' . esc_url(get_the_permalink()) . '" class="absolute" aria-label="' . esc_attr(get_the_title()) . '"></a></div>';
}

/*=========================================
//		svg contents
=========================================*/
function svg_contents($path, $class = "") {
    $svg = get_template_directory() . '/images/icons/' . $path . '.svg';
    if (file_exists($svg)) {
        ob_start();
        $content = file_get_contents($svg);
        $content = str_replace('xmlns="https://www.w3.org/2000/svg"', 'class="' . $class . '"', $content);
        $content = str_replace('xmlns="http://www.w3.org/2000/svg"', 'class="' . $class . '"', $content);
        ob_end_clean();
        return $content;
    } else {
        return '&#127303;';
    }
}

/* ===========================================================
// metadata post
=========================================================== */
function theme_get_metadata_icons($icons = '', $class = '') {
    if (empty($icons)) {
        return '';
    }
    if (is_string($icons)) {
        $icons = array_map('trim', explode(',', $icons));
    }
    if (!is_array($icons) || empty($icons)) {
        return '';
    }
    $output = [];
    foreach ($icons as $icon) {
        switch ($icon) {
            case 'date':
                $output[] = sprintf(
                    '<span class="theme-postdateicon"><time class="entry-date updated" datetime="%s" itemprop="datePublished">%s</time></span>',
                    esc_attr(get_the_date('c')),
                    esc_html(get_the_date())
                );
                break;
            case 'author':
                $author_id = get_the_author_meta('ID');
                $output[] = sprintf(
                    '<span class="theme-postauthoricon"><a class="author url fn" href="%s" rel="author" itemprop="author">%s</a></span>',
                    esc_url(get_author_posts_url($author_id)),
                    esc_html(get_the_author())
                );
                break;
            case 'category':
                $categories = get_the_category();
                if (!empty($categories)) {
                    $list = '<ul class="theme-categories" itemprop="articleSection">';
                    foreach ($categories as $category) {
                        $list .= sprintf(
                            '<li><a href="%s">%s</a></li>',
                            esc_url(get_category_link($category->term_id)),
                            esc_html($category->name)
                        );
                    }
                    $list .= '</ul>';
                    $output[] = $list;
                }
                break;
            case 'tag':
                $tags = get_the_tags();
                if (!empty($tags)) {
                    $list = '<ul class="theme-tags">';
                    foreach ($tags as $tag) {
                        $list .= sprintf(
                            '<li><a href="%s">%s</a></li>',
                            esc_url(get_tag_link($tag->term_id)),
                            esc_html($tag->name)
                        );
                    }
                    $list .= '</ul>';
                    $output[] = $list;
                }
                break;
            case 'edit':
                if (current_user_can('edit_post', get_the_ID())) {
                    $edit_link = get_edit_post_link();
                    if ($edit_link) {
                        $output[] = sprintf(
                            '<span class="theme-postediticon"><a href="%s">%s</a></span>',
                            esc_url($edit_link),
                            esc_html__('Edit', 'theme_textdomain')
                        );
                    }
                }
                break;
        }
    }
    if (empty($output)) {
        return '';
    }
    return sprintf(
        '<div class="theme-posticons theme-metadata %s">%s</div>',
        esc_attr($class),
        implode(' &nbsp; ', $output)
    );
}

/* ===========================================================
// 
=========================================================== */

function theme_get_post_id() {
    $post_id = theme_get_the_ID();
    if ($post_id != '') {
        $post_id = 'post-' . $post_id;
    }
    return $post_id;
}
function theme_get_the_ID() {
    global $post;
    return $post->ID;
}
function theme_get_post_class() {
    return implode(' ', get_post_class());
}

/* =================================================
		// autor single
==================================================*/
function get_author_profile($post_id = '2') {
    $author_id = get_post_field('post_author', $post_id);
    $author_data = get_userdata($author_id);

    if ($author_data) {
        
        $author_name = $author_data->display_name;
        $author_description = $author_data->description;
        $author_link = get_author_posts_url($author_id);
        $author_specialty = get_user_meta($author_id, 'especialidade', true);
        $author_crm = get_user_meta($author_id, 'crm', true);
        $author_lattes = get_user_meta($author_id, 'lattes', true);
        $author_linkedin = get_user_meta($author_id, 'linkedin', true);
        $author_doctoralia = get_user_meta($author_id, 'doctoralia', true);

        $author_profile = '<div class="author-profile g-author" itemscope itemtype="https://schema.org/Person">';

        if (wp_get_attachment_image(get_user_meta($author_id, 'user_attachment_id', true))) {
            $author_profile .= '<div class="author-avatar"><div class="circle bg" data-bg="' . wp_get_attachment_image_url(get_user_meta($author_id, 'user_attachment_id', true)) . '"></div></div>';
        } else {
            $author_profile .= '<div class="author-avatar"><div class="circle bg" data-bg="' . THEME_DIR . '/images/no-avatar.jpg"></div></div>';
        }

        $author_profile .= '<div class="author-info">';
        $author_profile .= '<p class="author-name fw6 mb5 text-black" itemprop="name">Publicado por: <a href="' . esc_url($author_link) . '">' . $author_name . '</a></p>';
        $author_profile .= '<p class="author-description">' . $author_description . '</p>';

        if (!empty($author_specialty)) {
            $author_profile .= '<div class="author-specialty" itemprop="jobTitle">Especialidade: ' . $author_specialty . '</div>';
        }

        if (!empty($author_crm)) {
            $author_profile .= '<div class="author-crm">CRM: <span itemprop="identifier">' . $author_crm . '</span></div>';
        }

        if (!empty($author_lattes)) {
            $author_profile .= '<div class="author-lattes">Lattes: <a href="' . $author_lattes . '" itemprop="sameAs">' . $author_lattes . '</a></div>';
        }

        if (!empty($author_linkedin)) {
            $author_profile .= '<div class="author-linkedin">LinkedIn: <a href="' . $author_linkedin . '" itemprop="sameAs">' . $author_linkedin . '</a></div>';
        }

        if (!empty($author_doctoralia)) {
            $author_profile .= '<div class="author-doctoralia">Doctoralia: <a href="' . $author_doctoralia . '" itemprop="sameAs">' . $author_doctoralia . '</a></div>';
        }

        $author_profile .= '</div>'; 
        $author_profile .= '</div>'; 

        return $author_profile;
    }

    return ''; 
}

/* =================================================
//	Cookie Consent
==================================================*/
function cookieConsent() {
if (!is_user_logged_in() && get_site_option('displaycookies') === '1') {
?>
<div class="cookieConsentContainer shadow hidden" id="cookieConsentContainer">
	<div class="cookieTitle"><?php echo get_site_option('title_cookies'); ?></div>
	<div class="cookieDesc">
		<p><?php echo get_site_option('text_cookies'); ?> <a href="<?php echo get_privacy_policy_url(); ?>" aria-label="<?php _e('More information about our privacy policy', THEME_NS); ?>" target="_blank"><?php _e('Privacy Policy', THEME_NS); ?></a></p>
	</div>
	<div class="cookieButton">
		<span onclick="consentGrantedAdStorage()" aria-label="<?php _e('Close and accept cookies', THEME_NS); ?>" class="btn btn-block btn-sm fw5 fz13 mt10"><?php _e('Yes, I accept cookies', THEME_NS); ?></span>
	</div>
</div>

<script>function consentGrantedAdStorage() {if (typeof gtag === 'function') {gtag('consent', 'update', {'ad_storage': 'granted','ad_user_data': 'granted','ad_personalization': 'granted','analytics_storage': 'granted'});} else {console.log('A função gtag não existe.');}localStorage.setItem('cookiesprivacy', 'true');document.getElementById('cookieConsentContainer').remove();}</script>
<?php
    }
}
add_action('wp_footer', 'cookieConsent');

/*=========================================
//		contact
=========================================*/
function theme_email() {
    $fields = [
        'nome' => 'Nome',
        'email' => 'E-mail',
        'telefone' => 'Telefone',
        'servico' => 'Serviço',
        'mensagem' => 'Mensagem',
        'site' => 'Url da página'
    ];

    if (isset($_POST['test_form']) && isset($_POST['email']) && !empty($_POST['email'])) {
        $resposta = isset($_POST['resposta']) ? sanitize_text_field($_POST['resposta']) : "Obrigado pelo contato, em breve retornaremos para você.";
        $message = '';
        $meta_data = [];

        // Salvar os dados no post type 'contatos'
        $new_post = array(
            'post_title'   => sanitize_text_field($_POST['nome']),
            'post_content' => $message,
            'post_status'  => 'publish',
            'post_type'    => 'leads',
        );
        $post_id = wp_insert_post($new_post);

        if (!is_wp_error($post_id)) {
            foreach ($_POST as $key => $value) {
                if ($key != 'resposta' && $key != 'assunto') {
                    // Tratar campos que podem ser arrays (como 'servico')
                    if (is_array($value)) {
                        $sanitized_value = array_map('sanitize_text_field', $value); // Sanitizar cada elemento do array
                        update_post_meta($post_id, $key, implode(', ', $sanitized_value)); // Armazenar como string separada por vírgula
                    } else {
                        update_post_meta($post_id, $key, sanitize_text_field($value));
                    }
                }
            }
        }

        foreach ($fields as $field => $label) {
            if (isset($_POST[$field])) {
                if (is_array($_POST[$field])) {
                    // Se for um array (como 'servico'), unir os valores com vírgula
                    $sanitized_value = array_map('sanitize_text_field', $_POST[$field]);
                    $message .= $label . ": " . implode(', ', $sanitized_value) . "<br>";
                    $meta_data[$field] = implode(', ', $sanitized_value);
                } elseif (!empty($_POST[$field])) {
                    // Caso contrário, processar como valor normal
                    $sanitized_value = sanitize_text_field($_POST[$field]);
                    $message .= $label . ": " . $sanitized_value . "<br>";
                    $meta_data[$field] = $sanitized_value;
                }
            }
        }

        $to = (get_site_option('email1') ? get_site_option('email1') : get_option('admin_email'));
        $subject = isset($_POST['assunto']) ? sanitize_text_field($_POST['assunto']) : "Contato pelo site - " . esc_url(get_bloginfo('name'));
        $headers[] = 'From: <' . $to . '>';
        $headers[] = 'Reply-To: ' . sanitize_text_field($_POST['nome']) . ' <' . sanitize_email($_POST['email']) . '>';
        $headers[] = "Content-Type: text/html; charset=UTF-8";

        $sent = wp_mail($to, $subject, $message, $headers);
        $close_message = '<span class="btn btn-xs ml10" onclick="document.getElementById(\'mail_message\').remove();">Fechar</span>';

        if ($sent) {
            echo '<div id="mail_message" class="mail-message">' . $resposta . ' ' . $close_message . '</div>';
        } else {
            echo '<div id="mail_message" class="mail-message error">Algo deu errado, tente novamente ' . $close_message . '</div>';
        }
    }
}

/* ===========================================================
// exibir elementos flutuantes footer
=========================================================== */
add_action('wp_footer', 'display_lements_flutants');
function display_lements_flutants() {
    // Código para o botão de voltar ao topo
    echo '<span class="scroll-top scroll text-white" data-offset="0" data-anchor="top" style="opacity:0" aria-label="Voltar ao topo"><span class="icon icon-up"></span></span>';

    // Código para o botão do WhatsApp
    if (get_site_option('fone2')) {
        $fone2 = preg_replace("/[^0-9]/", "", get_site_option('fone2'));
        echo '<div class="whatsapp-action">';
        echo '<a href="https://api.whatsapp.com/send?phone=55' . $fone2 . '&text=' . urlencode("Gostaria de informações") . '" class="wa whatsapp-link" rel="nofollow" target="_blank" aria-label="Contato pelo WhatsApp"><span id="btn-whatsapp" class="fz36 icon icon-whatsapp text-white"></span></a>';
        echo '</div>';
    }
}
/* ===========================================================
// rodapé direitos reservados
=========================================================== */
function all_right_reserved() {
    echo '<div class="all-right">';
    echo '<div class="reserved">';
    echo '<p>' . date('Y') . ' &copy; ' . get_bloginfo('name') . ' - Todos os direitos Reservados</p>';
    echo '</div>';
    echo '<div class="text-right">';
    echo '<p><a href="https://elton.disner.com.br/" id="dev" target="_blank" aria-label="Elton Disner: Programação e criação de sites WordPress">';
    echo '<img src="' . wp_cdn(THEME_DIR) . '/images/useless/logo.png?ssl=1" height="40" width="36" alt="Elton Disner: Programação e criação de sites WordPress"></a></p>';
    echo '</div>';
    echo '</div>';
}
