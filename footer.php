<footer id="colophon" class="site-footer" itemscope="itemscope" itemtype="https://schema.org/WPFooter">
	<div class="inner pt70">
		<div class="g cg4 rg4">
			<div class="md3 sm3">
				<a href="<?php echo esc_url(site_url()); ?>/" class="logo-link" rel="home" itemprop="url"><span itemprop="name" class="hidden"><?php bloginfo('name'); ?></span>
					<picture>
						<source srcset="<?php echo get_template_directory_uri(); ?>/images/logo.webp" type="image/webp">
						<source srcset="<?php echo get_template_directory_uri(); ?>/images/logo.png" type="image/jpeg">
						<img width="228" height="60" itemprop="logo" class="logo" alt="<?php bloginfo('name'); ?>" src="<?php echo get_template_directory_uri(); ?>/images/logo.png">
					</picture>
				</a>
			</div>
			<div class="md4 sm4">
				<?php social('social-footer') ?>
				<div class="mt15"><a href="mailto:<?php echo esc_html(antispambot(get_site_option('email1'))); ?>" target="_blank" class="flex-contato"><span class="icon icon-mail"></span><span><?php echo esc_html(antispambot(get_site_option('email1'))) ; ?></span></a></div>
				<div class="mt15"><a href="tel:<?php $fone1 = preg_replace("/[^0-9]/", "", get_site_option('fone1')); echo $fone1; ?>" target="_blank" class="flex-contato"><span class="icon icon-phone"></span><span><?php echo get_site_option('fone1'); ?></span></a></div>
			</div>
			<div class="md5 sm5"><?php wp_nav_menu(array('theme_location'=>'footer-menu','menu_id'=>'footer-menu'));?></div>
		</div>
	</div>
	<div class="copy">
		<div class="inner">
			<?php all_right_reserved(); ?>
		</div>
	</div>

</footer>

<div class="sidemenu hmd hidden-xl">
	<nav id="menu"><?php wp_nav_menu(array('theme_location'=>'footer-menu','menu_id'=>'mobile-menu'));?></nav>
	<hr class="mt20">
	<?php social('social-footer') ?>
	<hr class="mb30">
	<div class="mt15"><a href="mailto:<?php echo esc_html(antispambot(get_site_option('email1'))); ?>" target="_blank" class="flex-contato"><span class="icon icon-mail"></span><span><?php echo esc_html(antispambot(get_site_option('email1'))) ; ?></span></a></div>
	<div class="mt15"><a href="tel:<?php $fone1 = preg_replace("/[^0-9]/", "", get_site_option('fone1')); echo $fone1; ?>" target="_blank" class="flex-contato"><span class="icon icon-phone"></span><span><?php echo get_site_option('fone1'); ?></span></a></div>

</div>

<div id="wp-footer"></div>
<?php wp_footer(); ?>

</body>

</html>
