<?php

global $wp_version;
define('WP_VERSION', $wp_version);
define('THEME_DIR', get_template_directory_uri());

define('THEME_NS', 'eugeniomussak');
function textdomain_theme_setup() {
    load_theme_textdomain(THEME_NS, get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'textdomain_theme_setup');

if (function_exists('mb_internal_encoding')) {
    mb_internal_encoding(get_bloginfo('charset'));
}
if (function_exists('mb_regex_encoding')) {
    mb_regex_encoding(get_bloginfo('charset'));
}
/*=========================================
//		WordPree check Version
=========================================*/
if (WP_VERSION < 6) {
    add_action('admin_notices', 'theme_unsupported_version_notice1');
    add_action('wp_head', 'theme_unsupported_version_notice2');
    function theme_unsupported_version_notice1() {
?>
	<div id='theme-warning' class='error fade'>
		<p><strong><?php _e('Current theme requires WordPress 6.2.5 or higher.', THEME_NS); ?></strong>
			<?php echo sprintf(__('Please <a href="%s">upgrade WordPress</a>', THEME_NS), 'https://wordpress.org/support/article/updating-wordpress/'); ?>
		</p>
	</div>
<?php
    }
    function theme_unsupported_version_notice2() {
			echo '</head><body>'; _e('Current theme requires WordPress 6.2.5 or higher.', THEME_NS);
			echo sprintf(__('Please <a href="%s">upgrade WordPress</a>', THEME_NS), 'https://wordpress.org/support/article/updating-wordpress/');
			echo '</body></html>'; die();
    }
  return;
}
/* ===========================================================
// Permitir uploads de SVG, WEBP e PDF no WordPress
=========================================================== */
function custom_mime_types($mimes) {
    // Adicionar tipos MIME
    $mimes['svg']  = 'image/svg+xml';
    $mimes['webp'] = 'image/webp';
    $mimes['pdf']  = 'application/pdf';

    return $mimes;
}
add_filter('upload_mimes', 'custom_mime_types');

// Verificar segurança de arquivos SVG
function sanitize_svg($data, $file, $filename, $mimes) {
    if (isset($mimes['svg']) && $file['type'] === 'image/svg+xml') {
        $svg = file_get_contents($file['tmp_name']);

        // Remove códigos potencialmente perigosos
        $svg = preg_replace('/<script.*?<\/script>/is', '', $svg);
        $svg = preg_replace('/<\?.*?\?>/is', '', $svg);

        file_put_contents($file['tmp_name'], $svg);
    }

    return $data;
}
add_filter('wp_check_filetype_and_ext', 'sanitize_svg', 10, 4);

// Filtrar arquivos SVG para exibir no Media Library
function svg_allowed_in_media_library() {
    echo '<style>.media-icon img[src$=".svg"] {width: 100%; height: auto;}</style>';
}
add_action('admin_head', 'svg_allowed_in_media_library');

/*=========================================
// Função change_speech
// Altera o idioma da aplicação com base na preferência do usuário.
// Utiliza sessão para armazenar o idioma e valida a entrada para maior segurança.
//=========================================*/
add_action('init', 'change_speech', 0);
function change_speech() {
    $languages = ['pt_BR', 'en_US', 'es_ES'];
    // Detecta via GET
    $lang = filter_input(INPUT_GET, 'lang', FILTER_DEFAULT);
    if ($lang && in_array($lang, $languages)) {
        setcookie('site_lang', $lang, time() + WEEK_IN_SECONDS, COOKIEPATH, COOKIE_DOMAIN);
        $_COOKIE['site_lang'] = $lang; // Garante acesso imediato
    }
    $current_lang = $_COOKIE['site_lang'] ?? 'pt_BR';
    if (!in_array($current_lang, $languages)) {
        $current_lang = 'pt_BR';
    }
    switch_to_locale($current_lang);
}
/* ===========================================================
// 
=========================================================== */
function get_translated_field($post_id, $field_base) {
    $locale = get_locale();
    if ($locale === 'en_US') {
        $translated = get_post_meta($post_id, $field_base . '_en', true);
        return $translated ?: get_post_meta($post_id, $field_base, true); // fallback
    }
    return get_post_meta($post_id, $field_base, true);
}
/*=========================================
//		
=========================================*/
function set_auth_cookie_expiration($expiration, $user_id, $remember) {
    $new_expiration = 90 * DAY_IN_SECONDS;
    if ($remember) {
        $new_expiration = 90 * DAY_IN_SECONDS;
    }
    return $new_expiration;
}
add_filter('auth_cookie_expiration', 'set_auth_cookie_expiration', 10, 3);


/*=========================================
//		
=========================================*/
function wp_cdn($url){
    if (filter_var($url, FILTER_VALIDATE_URL) === FALSE) {
        return $url;
    }
    $cdn_url = get_site_option('api_cdn');
    if (empty($cdn_url)) {
        return $url;
    }
    if (strpos($url, 'https://') === 0 && strpos($url, 'https://') !== false) {
        return str_replace('https://', $cdn_url, $url);
    }
    return $url;
}


locate_template(array('lib/customize.php'), true);
if (is_admin()){
locate_template(array('lib/admins.php'), true);
} else {
		locate_template(array('lib/content.php'), true);
}


add_action( 'after_setup_theme', 'theme_add_supports' );
function theme_add_supports() {
	remove_filter('pre_oembed_result', 'wp_filter_pre_oembed_result', 10);
	add_theme_support('post-thumbnails');
	add_theme_support('nav-menus');
	add_theme_support('title-tag');
	add_theme_support('html5', array('search-form'));
	register_nav_menus(array(
		'primary-menu' => __('Primary Navigation', THEME_NS),
		'footer-menu' => __('Footer Navigation', THEME_NS),
	));
}

add_action('admin_head', 'custom_css_input_postbox');
add_action('login_enqueue_scripts', 'e_login_logo');
function custom_css_input_postbox() {
    echo '<link rel="stylesheet" href="' . THEME_DIR . '/css/admin/admin.css">';
}

function e_login_logo() { ?>
<style>.login h1 a{background:url(<?php echo get_template_directory_uri(); ?>/images/logo.png) 50% 50%/auto 100% no-repeat!important;padding-bottom:10px;width:250px!important;color: transparent;display: inline-flex;}.login label{color:#333!important;font-size:14px}.input,.login form input[type=checkbox],.login input[type=text]{background:rgba(255,255,255,.6)!important;box-shadow:0 0 3px inset rgba(0,0,0,.9)!important;border:1px solid #fff;border-radius:4px}.login form{box-shadow:none!important;border:0!important;border-radius:4px}.wp-core-ui .button-primary,body.login{background:#d9e2e8}</style>
<?php
}
function theme_favicon() {
   if (is_file(get_template_directory() . '/images/fav/favicon-16x16.png')): ?>
		<link rel="shortcut icon" href="<?php echo wp_cdn(THEME_DIR.'/images/fav/favicon-16x16.png'); ?>" type="image/x-icon">
		<link rel="icon" type="image/png" sizes="32x32" href="<?php echo wp_cdn(THEME_DIR.'/images/fav/favicon-32x32.png'); ?>">
		<meta name="msapplication-TileImage" content="<?php echo wp_cdn(THEME_DIR.'/images/fav/ms-icon-144x144.png'); ?>">
		<link rel="icon" type="image/png" sizes="192x192"  href="<?php echo wp_cdn(THEME_DIR.'/images/fav/android-icon-192x192.png'); ?>">
		<link rel="apple-touch-icon" href="<?php echo wp_cdn(THEME_DIR.'/images/fav/apple-icon-180x180.png'); ?>" />
		<meta rel="icon" type="image/png" sizes="270x270" content="<?php echo wp_cdn(THEME_DIR.'/images/fav/favicon-270x270.png'); ?>" />
		<meta name="msapplication-TileColor" content="#000">
		<meta name="theme-color" content="#000">
	<?php endif;
}
add_action('wp_head', 'theme_favicon');
add_action('admin_head', 'theme_favicon');
add_action('login_head', 'theme_favicon');
/*=========================================*/
//
/*=========================================*/
function theme_get_search() {
    theme_ob_start();
    get_search_form();
    return theme_ob_get_clean();
}
function theme_404_content() {
    echo '<h3>' . __('Not found', 'THEME_NS') . '</h3>';
    echo '<p>' . __('The page you requested was not found.', 'THEME_NS') . '</p>';
}

/*=========================================*/
// Disable Image Attachment Page
/*=========================================*/
function e_redirect_attachment_page() {
    if (is_attachment()) {
        global $post;
        if ($post && $post->post_parent) {
            wp_redirect(esc_url(get_permalink($post->post_parent)), 301);
            exit;
        } else {
            wp_redirect(esc_url(home_url('/')), 301);
            exit;
        }
    }
}
add_action('template_redirect', 'e_redirect_attachment_page');
/*=========================================
//		prevent empty search
=========================================*/
add_filter('posts_search', 'exclude_empty_search', 10, 2);
function exclude_empty_search($search, $q){
    if (!is_admin() && $q->is_search() && $q->is_main_query() && empty($q->query_vars['s'])) {
        $search .= " AND 0=1 ";
    }

    return $search;
}
