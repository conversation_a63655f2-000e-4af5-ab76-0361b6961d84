<aside id="intro" class="z0">
	<!-- <div id="bg" class="bg"></div> -->
	<div id="bg" class="bg" style="background-image: url(<?php echo THEME_DIR; ?>/images/useless/2.png);box-shadow: 0px 0px 800px #000 inset;"></div>
	<div class="intro-entry">
		<div class="inner text-balance">
			<div class="g mdoff2">
				<div class="text-center text-balance">
					<!-- <div class="circle bg" data-bg="<?php echo get_post_meta(get_the_ID(), 'intro_img', true)?>"></div> -->
					<!-- <img src="<?php echo THEME_DIR; ?>/images/logo-intro.png" alt="<?php bloginfo('name'); ?>" class="logo-intro" decoding="async" loading="lazy" fetchpriority="low"> -->
					<?php echo str_replace(array('h2','<a'), array('h1','<a class="btn btn-verde m10"'), apply_filters( 'the_content', get_post_meta(get_the_ID(), 'intro_text', true)));?>
					<a href="<?php echo get_post_meta(get_the_ID(), 'link_contato', true); ?>" class="btn btn-lg">Quero transformar minha liderança</a>
				</div>
			</div>
		</div>
	</div>
</aside>
<?php if (get_post_meta(get_the_ID(), 'palavras', true)){ echo '<script>let texto = "'.get_post_meta(get_the_ID(), 'palavras', true).'";let palavras = texto.split(",").map(p => p.trim());</script>'; } ?>
