/* @font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 200;
  src: url('../fonts/poppins-v23-latin-200.woff2') format('woff2'); 
} */

 @font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 300;
  src: url('../fonts/poppins-v23-latin-300.woff2') format('woff2'); 
} 

@font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/poppins-v23-latin-regular.woff2') format('woff2'); 
}

/* @font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  src: url('../fonts/poppins-v23-latin-500.woff2') format('woff2'); 
}
*/
@font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  src: url('../fonts/poppins-v23-latin-600.woff2') format('woff2'); 
} 

/* @font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/poppins-v23-latin-700.woff2') format('woff2'); 
} */

/* @font-face {
  font-display: swap; 
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 800;
  src: url('../fonts/poppins-v23-latin-800.woff2') format('woff2'); 
} */
/*==================================*/
// icons
/*==================================*/
@font-face {
	font-family: 'icons';
	src:
		url('../fonts/icons/icons.ttf') format('truetype'),
		url('../fonts/icons/icons.woff') format('woff');
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}
.icon {
	font-family: 'icons' !important;
	vertical-align: middle;
	speak: never;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	font-size: 20px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	position: relative;
	&.icon-close:before {
		content: "\e900";
	}
	&.icon-up:before {
		content: "\e901";
	}
	&.icon-pinterest:before {
		content: "\e902";
	}
	&.icon-user:before {
		content: "\e903";
	}
	&.icon-calendar:before {
		content: "\e904";
	}
	&.icon-date:before {
		content: "\e904";
	}
	&.icon-tiktok:before {
		content: "\e905";
	}
	&.icon-youtube:before {
		content: "\e906";
	}
	&.icon-whatsapp:before {
		content: "\e907";
	}
	&.icon-play:before {
		content: "\e91a";
	}
	&.icon-sun:before {
		content: "\e916";
	}
	&.icon-moon:before {
		content: "\e917";
	}
	&.icon-share:before {
		content: "\e918";
	}
	&.icon-external-link:before {
		content: "\e919";
	}
	&.icon-check:before {
		content: "\e908";
	}
	&.icon-clock:before {
		content: "\e909";
	}
	&.icon-linkedin:before {
		content: "\e90a";
	}
	&.icon-search:before {
		content: "\e90b";
	}
	&.icon-instagram:before {
		content: "\e90c";
	}
	&.icon-down:before {
		content: "\e90d";
	}
	&.icon-right:before {
		content: "\e90e";
	}
	&.icon-left:before {
		content: "\e90f";
	}
	&.icon-map:before {
		content: "\e910";
	}
	&.icon-mail:before {
		content: "\e911";
	}
	&.icon-phone:before {
		content: "\e912";
	}
	&.icon-instagram1:before {
		content: "\e913";
	}
	&.icon-twitter:before {
		content: "\e914";
	}
	&.icon-facebook:before {
		content: "\e915";
	}
}