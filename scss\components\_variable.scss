﻿// functions scss
@function r($value) {
	@return ($value / 16)+rem;
}
@function v($var) {
	@return var(--#{$var});
}
/*=========================================
//		css variables
=========================================*/
:root {
	// colors
	--white: #fff;
	--primary: #0045ec;
	--secondary: #1ec7e2;
	--gray: #e0e0e0;
	--dark: #7a7a7a;
	--black: #000000;
	--hover: #851195;
	--gradient: linear-gradient(90deg, var(--primary), var(--secondary), var(--hover));
	--webkit-gradient: webkit-linear-gradient(90deg, var(--primary), var(--secondary), var(--hover));
	// fonts
	--fontDefault: "Poppins", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, sans-serif;
	--fontTitle: "Poppins", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, sans-serif;
	--font-monospace: ui-monospace, "Cascadia Code", "Source Code Pro", Menlo, Consolas, "DejaVu Sans Mono", monospace;
	--font-serif: georgia, Times New Roman, serif;
	// grid
	--g2: repeat(2, minmax(0, 1fr));
	--g3: repeat(3, minmax(0, 1fr));
	--g4: repeat(4, minmax(0, 1fr));
	--g5: repeat(5, minmax(0, 1fr));
	--g6: repeat(6, minmax(0, 1fr));
	// transition
	--duration: 1s;
	--easing: cubic-bezier(0.53, 0.46, 0.17, 0.9);
}