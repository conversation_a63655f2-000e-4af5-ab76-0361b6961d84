/* ==========================================================================
   FORMULÁRIOS - ESTILOS BASE
   ========================================================================== */
// Campos de entrada básicos
input,
select,
textarea {
	width: 100%;
	height: 48px;
	padding: 0px 12px;
	margin: 0 0 15px;
	font-size: 16px;
	line-height: 46px;
	color: var(--dark);
	background: var(--gray);
	border: 2px solid var(--gray);
	border-radius: 4px;
	filter: brightness(0.95);
	transition: all 0.8s ease;
	will-change: auto;
	&:focus {
		border-color: var(--dark);
	}
}
// Placeholder
::-webkit-input-placeholder,
::-moz-placeholder,
:-ms-input-placeholder,
:-moz-placeholder,
:input-placeholder,
:placeholder {
	color: var(--dark);
}
// Correção para autopreenchimento
input:-webkit-autofill {
	-webkit-box-shadow: 0 0 0px 1000px #f2f2f2 inset;
}
// Textarea
textarea {
	padding: 7px 15px 0;
	height: 100px;
	line-height: 1.28;
	&:focus,
	&.form-control:focus {
		padding-top: 15px;
		padding-bottom: 10px;
		outline: 0;
		box-shadow: none;
	}
}
// Legenda
legend {
	display: block;
	width: 100%;
	margin-bottom: 20px;
	font-size: 21px;
	line-height: inherit;
	color: var(--dark);
	border-bottom: 1px solid #e5e5e5;
}
// Rótulos
label {
	display: inline-block;
	margin-bottom: 5px;
}
/* ==========================================================================
   CHECKBOX E RADIO
   ========================================================================== */
// Estilos base para checkbox e radio
input[type="radio"],
input[type="checkbox"] {
	position: relative;
	width: 26px;
	height: 26px;
	margin: 0 10px 0 0;
	line-height: normal;
	vertical-align: middle;
	box-shadow: none;
	&:before {
		position: absolute;
		top: 0;
		left: 0;
		display: inline-block;
		width: 26px;
		height: 26px;
		font-size: 24px;
		font-weight: 800;
		line-height: 24px;
		color: var(--primary);
		text-align: center;
		cursor: pointer;
		background-color: var(--gray);
		background-position: center;
		background-repeat: no-repeat;
		background-size: cover;
		border: 2px solid var(--dark);
		border-radius: 0px;
		content: "";
		transition: all 0.6s ease;
		will-change: auto;
	}
	&:hover:before {
		border-color: var(--hover);
		color: var(--hover);
	}
}
// Específico para radio
input[type="radio"] {
	&:before {
		border-radius: 50%;
		line-height: 15px;
		font-size: 26px;
	}
	&:checked:before {
		background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjMDAwIiBkPSJNMTIgMkM2LjQ3IDIgMiA2LjQ3IDIgMTJzNC40NyAxMCAxMCAxMHMxMC00LjQ3IDEwLTEwUzE3LjUzIDIgMTIgMiIvPjwvc3ZnPg==");
	}
}
// Específico para checkbox
input[type="checkbox"] {
	&:checked:before {
		content: "";
		background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjMDAwIiBkPSJtOS41NSAxOGwtNS43LTUuN2wxLjQyNS0xLjQyNUw5LjU1IDE1LjE1bDkuMTc1LTkuMTc1TDIwLjE1IDcuNHoiLz48L3N2Zz4=");
	}
}
// Containers de checkbox e radio
.checkbox,
.radio {
	position: relative;
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
	margin-right: 10px;
	label {
		min-height: 20px;
		padding-left: 20px;
		margin-bottom: 0;
		font-weight: 400;
		cursor: pointer;
	}
}
// Posicionamento de inputs dentro de checkbox/radio
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"],
.radio input[type="radio"],
.radio-inline input[type="radio"] {
	position: absolute;
	margin-left: -20px;
}
/* ==========================================================================
   OUTROS TIPOS DE INPUT
   ========================================================================== */
// Input file
input[type="file"] {
	display: block;
}
// Input range
input[type="range"] {
	display: block;
	width: 100%;
}
// Select múltiplo
select[multiple],
select[size] {
	height: auto;
}
/* ==========================================================================
   BOTÕES
   Base de estilização para todos os tipos de botões e inputs
   ========================================================================== */
// Estilo base para todos os botões e inputs do tipo botão
.btn,
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
	// Propriedades de layout
	position: relative;
	display: inline-flex;
	width: auto;
	min-height: 52px;
	padding: 10px 30px;
	margin-bottom: 0;
	align-items: center;
	justify-content: center;
	// Propriedades de texto
	line-height: 1.2;
	color: var(--black);
	text-align: center;
	white-space: normal;
	vertical-align: middle;
	text-wrap: balance;
	font-weight: 600;
	// Propriedades visuais
	overflow: hidden;
	background-color: transparent;
	border: 0px solid var(--black);
	border-radius: 34px;
	outline: 0;
	cursor: pointer;
	// Transições
	transition: all 0.55s ease-out;
	// Estados
	&.focus,
	&:focus,
	&:hover {
		color: var(--white);
		text-decoration: none;
		filter: grayscale(.75) saturate(1.5);
	}
	&.active,
	&:active {
		outline: 0;
		background-image: none;
		box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
	}
	&.disabled,
	&[disabled] {
		cursor: not-allowed;
		opacity: 0.65;
		box-shadow: none;
	}
	// Variações
	&.btn-primary {
		background-color: var(--primary);
		border-color: var(--primary);
		color: var(--white);
		svg path {
			fill: var(--white);
		}
	}
	&.btn-block {
		display: flex;
		width: 100%;
		justify-content: center;
		align-items: center;
	}
}
/* ==========================================================================
   VARIAÇÕES DE BOTÕES
   Diferentes estilos para os botões
   ========================================================================== */
// Botão padrão (fundo branco)
.btn-default {
	color: var(--black);
	background-color: var(--white);
	border-color: var(--white);
	&.active,
	&:active,
	&:hover {
		color: var(--white);
		background-color: var(--hover);
		border-color: var(--hover);
	}
}
// Botão outline (transparente com borda)
.btn-outline,
#intro p .btn:nth-child(2) {
	color: var(--primary);
	background-color: transparent;
	border: 2px solid var(--primary);
	&.active,
	&:active,
	&:hover {
		color: var(--white);
		background-color: var(--hover);
		border-color: var(--hover);
	}
}
// Botão link (sem borda)
.btn-link {
	color: var(--primary);
	border-radius: 0;
	border-color: transparent;
	&.active,
	&:active,
	&[disabled],
	&:hover,
	&:focus {
		background-color: transparent;
		box-shadow: none;
	}
	&:active,
	&:focus,
	&:hover {
		border-color: transparent;
	}
}
/* ==========================================================================
   TAMANHOS DE BOTÕES
   Diferentes tamanhos de botões
   ========================================================================== */
// Botão grande
.btn-lg {
	padding-left: 40px;
	padding-right: 40px;
	background: var(--webkit-gradient);
	background: var(--gradient);
	font-size: 16px;
	border: 0;
	color: var(--white);
	margin-top: 20px;
	&:hover {
		background-color: var(--secondary);
		color: #fff
	}
}
// Botão pequeno
.btn-sm {
	padding: 0px 10px;
	line-height: 32px;
}
// Botão extra pequeno
.btn-xs {
	padding: 0px 12px;
	line-height: 20px;
	min-height: 28px;
}
/* ==========================================================================
   COMPONENTES DE FORMULÁRIO ESPECIAIS
   ========================================================================== */
// Formulário de busca
form.e-search {
	position: relative;
	input[type="text"] {
		background: var(--white);
	}
	button {
		position: absolute;
		right: 5px;
		margin-bottom: 0px;
		text-align: center;
		background: transparent;
		border: 0;
	}
}
// Desabilitar links de botão
a.btn.disabled,
fieldset[disabled] a.btn {
	pointer-events: none;
}