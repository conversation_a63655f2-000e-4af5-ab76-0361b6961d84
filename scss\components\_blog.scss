.g-author {
  display: grid;
  gap: 0px 25px;
  grid-template-columns: 70px auto;
}
.wp-caption {
  margin-bottom: 1.5em;
 img[class*="wp-image-"] {
   display: block;
   margin-right: auto;
   margin-left: auto;
	}
 .wp-caption-text {
   margin: 0;
   padding: 7px;
   background: var(--gray);
	}
}
figure.wp-caption {
  max-width: 100%;
}
.thumb-box {
  width: 100%;
  padding-top: 100%;
}
.thumb-post {
  display: flex;
  position: relative;
  overflow: hidden;
  padding-top: 56%;
 img {
   -o-object-fit: cover;
   object-fit: cover;
   -o-object-position: center;
   object-position: center;
   will-change: auto;
   -webkit-transition: all 1s cubic-bezier(0.45, 0.45, 0, 1.2);
   position: absolute;
   top: 0;
   bottom: 0;
   left: 0;
   right: 0;
   width: 100%;
   height: 100%;
   transition: all 1s cubic-bezier(0.45, 0.45, 0, 1.2);
	}
}
.circle {
  position: relative;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding-top: 100%;
  border-radius: 100%;
}
.e-pager {
  position: relative;
  clear: both;
  margin: 20px 0;
 li {
   display: inline-block;
   vertical-align: middle;
	}
	a,
 span {
   display: inline-block;
   overflow: hidden;
   width: 40px;
   height: 40px;
   margin: 0 3px 6px;
   background-color: var(--gray);
   border: 0;
   border-radius: 3px;
   font-size: 16px;
   font-weight: 800;
   line-height: 38px;
   color: var(--dark);
   text-align: center;
  &.current {
    background: var(--white);
    color: var(--dark);
		}
  &:hover {
    background-color: var(--primary);
    color: var(--white);
		}
	}
 a.next.page-numbers {
   font-size: 0;
   color: var(--dark);
  &:before {
    content: "❯";
    font-size: 16px;
    color: var(--dark);
		}
  &:hover {
   &:before {
     color: var(--white);
			}
		}
	}
 a.prev.page-numbers {
   font-size: 0;
   color: var(--dark);
  &:before {
    content: "❮";
    font-size: 16px;
    color: var(--dark);
		}
  &:hover {
   &:before {
     color: var(--white);
			}
		}
	}
}
button.social_share {
  fill: var(--hover);
  width: 40px;
  height: 40px;
  margin: 4px;
  padding: 0;
  background-color: var(--gray);
  line-height: 40px;
 &:hover {
   fill: #fff;
   background-color: var(--hover);
	}
}
.share {
  display: block;
  position: relative;
  clear: both;
  margin: 0;
  padding: 0;
 a {
   -webkit-transition: all 0.5;
   display: inline-block;
   overflow: hidden;
   width: 40px;
   height: 40px;
   margin: 0 3px 6px;
   transition: all 0.5;
   background-color: var(--primary);
   border: 0;
   border-radius: 50%;
   font-size: 16px;
   font-weight: 800;
   line-height: 38px;
   color: var(--white);
   text-align: center;
  path {
    fill: var(--white);
		}
  svg {
    width: 22px;
    height: 22px;
		}
  &.active {
    background: var(--white);
   path {
     fill: var(--primary);
			}
		}
  &:hover {
    background: var(--white);
   path {
     fill: var(--primary);
			}
		}
	}
}
.single article .entry-content ul {
  margin: 15px 0 30px 15px;
 li {
   margin: 0 0 6px;
   list-style: disc;
	}
}
.author-description {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.3;
}
.post-img {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 290px;
  margin-bottom: 10px;
 a {
   position: absolute;
   top: 0;
   bottom: 0;
   left: 0;
   right: 0;
	}
}
.alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em;
}
.alignright {
  display: inline;
  float: right;
  margin-left: 1.5em;
}
.aligncenter {
  display: block;
  clear: both;
  margin-right: auto;
  margin-left: auto;
}
img.avatar {
  float: left;
  width: 60px;
  padding-right: 15px;
}
a.post-link {
  display: block;
  font-size: 22px;
  font-weight: 600;
  line-height: 1.42;
  color: var(--black);
}
.theme-post.fz14.text-black {
 a {
   color: var(--dark);
  &:hover {
    color: var(--hover);
		}
	}
}
h4.t {
  margin: 0 0 16px;
  font-size: 22px;
  font-weight: 600;
}
span.theme-categories {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 10px;
 span.categories {
   display: flex;
   align-items: center;
   column-gap: 20px;
	}
}