@charset "utf-8";
/* ==========================================================================
   RESET E ESTILOS BASE
   ========================================================================== */
html {
	scroll-behavior: smooth;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
*,
*:before,
*:after {
	padding: 0;
	margin: 0;
	list-style: none;
	box-sizing: border-box;
	outline: 0;
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizelegibility;
}
*:focus {
	outline: 0;
	box-shadow: none;
}
body {
	padding: 0;
	margin: 0;
	font-size: r(17);
	line-height: 1.5;
	border-radius: 0;
	font-weight: 400;
	color: var(--white);
	background-color: var(--black);
	font-family: var(--fontDefault);
	b,
	strong {
		font-weight: 600;
	}
}
#page {
	overflow: hidden;
	display: block;
}
/* ==========================================================================
   TIPOGRAFIA
   ========================================================================== */
h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: 300;
	line-height: 1.15;
	padding-top: 0;
	margin-top: 0;
	margin-bottom: 15px;
	clear: both;
	font-family: var(--fontTitle);
	b,
	strong {
		font-weight: 600;
	}
}
// Tamanhos de fonte para headings usando loop
$font-size: 44;
@for $i from 1 through 7 {
	h#{$i} {
		font-size: ($font-size / 16) * 1rem;
	}
	$font-size: $font-size - 4;
}
// Pesos de fonte
@for $i from 1 through 9 {
	.fw#{$i} {
		font-weight: $i * 100;
	}
}
// Tamanhos de fonte
$font-size: 10;
@while $font-size <=72 {
	.fz#{$font-size} {
		font-size: ($font-size / 16) + rem;
	}
	$font-size: $font-size+2;
}
p {
	margin: 0 0 20px;
	line-height: 1.4;
}
.title {
	font-family: var(--fontTitle);
}
code,
aside *,
form * {
	font-family: var(--fontDefault);
}
/* ==========================================================================
   ELEMENTOS HTML PADRÃO
   ========================================================================== */
a {
	text-decoration: none;
	outline: 0;
	box-shadow: none;
	color: var(--primary);
	transition: all 0.22s ease;
	&:active,
	&:focus,
	&:hover {
		color: var(--hover);
	}
	&:focus {
		outline: 0;
	}
	&.absolute {
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 1;
	}
}
ul,
li {
	padding: 0;
	margin: 0;
	list-style: none;
}
ul.inline,
ul.social {
	display: inline-flex;
	align-items: center;
	column-gap: 16px;
	li {
		display: inline-block;
		padding: 0;
		margin: 0;
		a {
			display: block;
			min-width: 32px;
		}
	}
}
li> {
	ol,
	ul {
		margin-bottom: 0;
		margin-left: 20px;
	}
}
hr {
	background-color: var(--dark);
	border: 0;
	height: 1px;
	margin: 15px 0;
	opacity: 0.4;
}
figure {
	padding: 0;
	margin: 0;
	width: 100%;
}
object,
picture,
iframe,
img,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
	display: block;
	display: block;
	border: 0;
	max-width: 100%;
	height: auto;
}
/* ==========================================================================
   CLASSES DE UTILIDADES
   ========================================================================== */
/* Posicionamento */
.pull-right {
	float: right;
}
.pull-left {
	float: left;
}
.left {
	left: 0;
}
.right {
	right: 0;
}
.top {
	top: 0;
}
.bottom {
	bottom: 0;
}
.absolute {
	position: absolute;
}
.relative {
	position: relative;
}
/* Z-Index */
.z-1 {
	z-index: -1;
}
.z0 {
	z-index: 0;
}
.z1 {
	z-index: 1;
}
.z2 {
	z-index: 2;
}
.z3 {
	z-index: 3;
}
/* Altura de linha */
.lineh1 {
	line-height: 1;
}
.lineh12 {
	line-height: 1.2;
}
.lineh14 {
	line-height: 1.4;
}
/* Alinhamento de texto */
.hidden {
	display: none !important;
}
.text-hide {
	font: 0/0 a;
	color: transparent;
	text-shadow: none;
	background-color: transparent;
	border: 0;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.text-balance {
	text-wrap: balance;
}
/* Cores de texto */
.text-white {
	color: var(--white);
}
.text-primary {
	color: var(--primary);
}
.text-secondary {
	color: var(--secondary);
}
.text-gray {
	color: var(--gray);
}
.text-dark {
	color: var(--dark);
}
.text-black {
	color: var(--black);
}
.text-hover {
	color: var(--hover);
}
.text-border {
	-webkit-text-stroke: 1px var(--primary);
	color: transparent;
}
/* Fundos */
.bg-white {
	background-color: var(--white);
}
.bg-primary {
	background-color: var(--primary);
}
.bg-secondary {
	background-color: var(--secondary);
}
.bg-black {
	background-color: var(--black);
}
.bg-dark {
	background-color: var(--dark);
}
.bg-gray {
	background-color: var(--gray);
}
.bg-hover {
	background-color: var(--hover);
}
.bg-gradient {
	background: var(--webkit-gradient);
	background: var(--gradient);
}
/* Backgrounds e imagens */
.bg,
img {
	transition: all 0.76s ease;
}
[data-bg], [dara-src] {
	opacity: .25;
	background: linear-gradient(45deg, #ffffff10, #000000a1, #ffffff10);
}
.bg {
	background-position: center;
	background-size: cover;
	background-repeat: no-repeat;
}
/* Border radius */
.radius5 {
	border-radius: 5px;
}
.radius10 {
	border-radius: 10px;
}
.radius15 {
	border-radius: 15px;
}
/* Outros */
.shadow {
	box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.15);
}
/* ==========================================================================
   SISTEMA DE ESPAÇAMENTO (MARGIN E PADDING)
   ========================================================================== */
$paddingMarginValues: ("p": "padding",
	"pt": "padding-top",
	"pb": "padding-bottom",
	"pl": "padding-left",
	"pr": "padding-right",
	"m": "margin",
	"mt": "margin-top",
	"mb": "margin-bottom",
	"ml": "margin-left",
	"mr": "margin-right"
);
// Incrementos de 5px (5, 10, 15, 20, 25, 30)
@for $i from 1 through 6 {
	$value: $i * 5;
	@each $class, $property in $paddingMarginValues {
		.#{$class}#{$value} {
			#{$property}: #{$value}px;
		}
	}
}
// Incrementos de 10px (10, 20, 30...100)
@for $i from 1 through 10 {
	$value: $i * 10;
	@each $class, $property in $paddingMarginValues {
		.#{$class}#{$value} {
			#{$property}: #{$value}px;
		}
	}
}
// Valor zero
.p0 {
	padding: 0px;
}
.m0 {
	margin: 0px;
}
/* ==========================================================================
   ANIMAÇÕES E TRANSIÇÕES
   ========================================================================== */
.inspect {
	opacity: 0.64;
	transform: translate3d(0px, 10px, 0px);
	will-change: transform, opacity;
	transition: all var(--duration) var(--easing);
	// Fade only (no movement)
	&.fade {
		transform: none;
		opacity: 0;
		transition: opacity var(--duration) var(--easing);
	}
	// From left
	&.move-left {
		transform: translate3d(-20px, 0px, 0px);
	}
	// From right
	&.move-right {
		transform: translate3d(20px, 0px, 0px);
	}
	// From top
	&.move-up {
		transform: translate3d(0px, -10px, 0px);
	}
	// From bottom
	&.move-down {
		transform: translate3d(0px, 10px, 0px);
	}
	// Scale up
	&.move-scale {
		transform: scale(0.95);
	}
	// Rotate and fade
	&.move-rotate {
		transform: rotate(-3deg);
	}
	// Bounce from bottom
	&.move-bounce {
		transition: all 0.8s cubic-bezier(0.2, 0.8, 0.3, 1.2);
	}
	// Different speeds
	&.move-fast {
		transition: all 0.5s var(--easing);
	}
	&.move-slow {
		transition: all 1.5s var(--easing);
	}
	// Common "in" state for all variations
	&.in {
		opacity: 1;
		transform: none;
	}
}
/* ==========================================================================
   COMPONENTES
   ========================================================================== */
/* Listas com checkmark */
.check ul,
ul.check {
	padding-left: 0;
	margin-bottom: 20px;
	li {
		padding-left: 32px;
		margin-bottom: 14px;
		line-height: 1.4;
		background-position: 0% 3px;
		background-repeat: no-repeat;
		background-size: 20px;
		background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSIjMWVjN2UyIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xMiAyMWE5IDkgMCAxIDAgMC0xOGE5IDkgMCAwIDAgMCAxOG0tLjIzMi01LjM2bDUtNmwtMS41MzYtMS4yOGwtNC4zIDUuMTU5bC0yLjIyNS0yLjIyNmwtMS40MTQgMS40MTRsMyAzbC43NzQuNzc0eiIgY2xpcC1ydWxlPSJldmVub2RkIi8+PC9zdmc+");
	}
}
/* Container de vídeo responsivo */
.video-container {
	position: relative;
	padding-bottom: 56.25%;
	height: 0;
	overflow: hidden;
	iframe,
	object,
	embed {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		padding: 0;
		background-color: var(--dark);
		border: 0;
		transition: all 0.5s ease;
		will-change: auto;
		&.iframe {
			opacity: 0;
		}
	}
}
span.play {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 72px;
	transform: translate(-50%, -50%);
}
/* ==========================================================================
   ESTRUTURA DE CONTEÚDO
   ========================================================================== */
main {
	position: relative;
	z-index: 1;
	.entry {
		b,
		strong {
			font-weight: 800;
		}
		ul {
			padding-left: 20px;
			margin-bottom: 30px;
			li {
				list-style: disc;
				margin-bottom: 10px;
			}
		}
		blockquote {
			padding: 25px 0 25px 25px;
			margin-left: 5px;
			font-weight: 400;
			border-left: 6px solid var(--primary);
			p {
				line-height: 1.2;
			}
		}
		ol {
			margin: 30px 0;
			padding: 0 0 0 25px;
			li {
				list-style: decimal;
			}
		}
	}
}
section {
	position: relative;
	width: 100%;
	padding: 0;
	margin: 0;
	.btn {
		margin-top: 10px;
	}
}
/* ==========================================================================
   ADMIN BAR (WORDPRESS)
   ========================================================================== */
#wpadminbar .ab-top-secondary .menupop .ab-sub-wrapper {
	max-width: none;
}
#wpadminbar * {
	color: #ccc;
}
li#wp-admin-bar-wp-logo,
li#wp-admin-bar-comments {
	display: none;
}
/* ==========================================================================
   CLASSES UTILITÁRIAS
   ========================================================================== */
// Texto para leitores de tela (acessibilidade)
.screen-reader-text {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(1px, 1px, 1px, 1px);
	word-wrap: normal;
	border: 0;
	clip-path: inset(50%);
	&:focus {
		top: 5px;
		left: 5px;
		z-index: 100000;
		display: block;
		width: auto;
		height: auto;
		padding: 15px 23px 14px;
		clip: auto;
		font-size: 14px;
		font-size: 0.875rem;
		font-weight: 700;
		line-height: normal;
		color: #21759b;
		text-decoration: none;
		background-color: #f1f1f1;
		border-radius: 4px;
		box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
		clip-path: none;
	}
}
// Clearfix
.clear {
	&:after,
	&:before {
		content: "";
		display: table;
		table-layout: fixed;
	}
	&:after {
		clear: both;
	}
}
// Classe de bloco
.block {
	width: 100%;
	display: block;
}