/* ==========================================================================
// containers
========================================================================== */
.inner:after,
.inner:before {
	display: table;
	content: " ";
}
.inner,
.inner-fluid {
	width: 1280px;
	max-width: 100%;
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto;
	.inner {
		padding-left: 0;
		padding-right: 0;
		width: 100%;
	}
}
.inner-fluid {
	width: 100%;
}
.clearfix:after,
.inner:after,
.g:after {
	clear: both;
}
/* ==========================================================================
// Alignments
========================================================================== */
.vmiddle {
	-webkit-box-align: center;
	align-items: center;
	vertical-align: middle;
}
.justify-center {
	-webkit-box-pack: center;
	justify-content: center;
}
.vbottom {
	vertical-align: bottom;
	-webkit-box-align: end;
	align-items: flex-end;
}
.vtop {
	vertical-align: top;
	-webkit-box-align: start;
	align-items: flex-start;
}
/* ==========================================================================
// Mixin for breakpoint-based column classes
========================================================================== */
@mixin grid-columns($size) {
	&.#{$size}2 {
		grid-template-columns: var(--g2);
	}
	&.#{$size}3 {
		grid-template-columns: var(--g3);
	}
	&.#{$size}4 {
		grid-template-columns: var(--g4);
	}
	&.#{$size}5 {
		grid-template-columns: var(--g5);
	}
	&.#{$size}6 {
		grid-template-columns: var(--g6);
	}
	&.#{$size}2,
	&.#{$size}3,
	&.#{$size}4,
	&.#{$size}5,
	&.#{$size}6 {
		> * {
			grid-column-start: auto;
		}
	}
}
/* ==========================================================================
// Mixin to control gaps
========================================================================== */
@mixin gap-classes($start: 0, $increment: 10px, $count: 6) {
	@for $i from $start through $count {
		&.cg#{$i} {
			column-gap: $i * $increment;
		}
		&.rg#{$i} {
			row-gap: $i * $increment;
		}
	}
}
/* ==========================================================================
// Base class for the default grid layout
========================================================================== */
.g {
	display: grid;
	column-gap: 15px;
	row-gap: 15px;
	grid-template-columns: repeat(12, 1fr);
	> * {
		grid-column-start: span 12;
		position: relative;
		min-height: 1px;
	}
	&.grid10 {
		grid-template-columns: repeat(10, 1fr);
	}
	@include gap-classes;
}
/* ==========================================================================
// Media query for large screens (>= 992px)
========================================================================== */
@media (min-width: 992px) {
	.g {
		@include grid-columns(md);
		@for $i from 1 through 4 {
			&.mdoff#{$i} {
				width: calc(100% - #{$i} * 10%);
				margin-left: auto;
				margin-right: auto;
			}
		}
		@for $i from 1 through 12 {
			.md#{$i} {
				grid-column-start: span #{$i};
			}
		}
	}
}
/* ==========================================================================
// Media query for medium screens (< 991px)
========================================================================== */
@media (min-width: 768px) and (max-width: 991px) {
}
/* ==========================================================================
// Media query for medium screens (768px - 991px)
========================================================================== */
@media (min-width: 768px) and (max-width: 991px) {
	.inner {
		width: 100%;
	}
	.g {
		@include grid-columns(sm);
		@for $i from 1 through 12 {
			.sm#{$i} {
				grid-column-start: span #{$i};
			}
		}
	}
	.hsm {
		display: none !important;
	}
}
/* ==========================================================================
// Media query for small screens (< 768px)
========================================================================== */
@media all and (max-width: 767px) {
	.hxs {
		display: none !important;
	}
	.g {
		@include grid-columns(xs);
		@for $i from 1 through 12 {
			.xs#{$i} {
				grid-column-start: span #{$i};
			}
		}
	}
	.g.cg2,
	.g.cg3,
	.g.cg4,
	.g.cg5,
	.g.cg6 {
		column-gap: 15px;
	}
	.invert-xs {
		//grid-column: 1;
		grid-row: 1;
	}
}
