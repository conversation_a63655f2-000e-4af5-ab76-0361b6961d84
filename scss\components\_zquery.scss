@media (min-width: 992px) {
	.hmd {
		display: none !important
	}
	.site-header {
		padding-top: 50px;
	}
	ul#footer-menu {
		display: grid;
		grid-template-columns: var(--g2);
		li {
			grid-template-columns: auto;
			padding-bottom: 10px;
		}
	}
}
@media (max-width: 991px) {
	html #wpadminbar {
		height: 0;
		display: none !important;
	}
	html[lang="pt-BR"] {
		margin-top: 0 !important;
	}
	.admin-bar .site-header {
		margin-top: 0;
	}
	aside#intro h1, #site-headline h1, h1, h2 {
		font-size: 36px;
	}
	aside#intro .btn, #site-headline .btn {
		padding-left: 20px;
		padding-right: 20px;
	}
	section#voce_conhece {
		margin-bottom: 60px;
	}
	section#conheca {
		margin-top: 40px;
	}
}
@media (min-width: 768px) and (max-width: 991px) {}
@media all and (max-width: 767px) {
	.site-branding .logo-link .logo {
		max-width: 120px;
		height: auto;
	}
	aside#intro h1, #site-headline h1, h1, h2, section#conheca h2, section#conheca h2 strong {
		font-size: 24px;
	}
	aside#intro .btn, #site-headline .btn, aside#intro, #site-headline {
		font-size: 14px;
	}
	.single .secondary {
		padding-top: 60px;
	}
	.site-footer .copy {
		padding-bottom: 70px;
	}
}
@media all and (max-width: 479px) {}
@media all and (max-width: 360px) {}