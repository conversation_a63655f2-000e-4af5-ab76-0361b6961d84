.site-header {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 9;
	height: auto;
	max-width: 100vw;
	padding: 0;
	color: var(--dark);
	padding-top: 10px;
	-webkit-transition: all .28s ease;
	transition: all .28s ease;
	a {
		color: var(--white);
		&:hover {
			color: var(--hover)
		}
	}
}
.site-branding {
	padding: 0;
	line-height: 44px;
	transition: all 0.8s ease;
	will-change: auto;
	-webkit-transition: all 0.8s ease;
	.logo-link {
		display: block;
		position: relative;
		.logo {
			height: 60px;
			width: auto;
			max-width: none;
		}
	}
	.g-branding {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 0 40px;
	}
}
.main-navigation {
	display: inline-block;
	position: relative;
	.menu {
		width: 100%;
		padding: 0;
		margin: 0;
		font-size: 16px;
		li {
			list-style: none;
			padding: 0;
			margin: 0;
			display: inline-block;
			position: relative;
			a {
				display: block;
				line-height: 100%;
				padding: 6px 0 6px 15px;
			}
			&.current_page_item a, &.current-menu-item a {
				color: var(--white);
			}
			&:hover a {
				color: var(--hover)
			}
			&.menu-item-has-children {
				margin-right: 15px;
				&:before {
					position: absolute;
					top: 16px;
					right: -15px;
					width: 10px;
					height: 10px;
					background-position: 50% 95%;
					background-repeat: no-repeat;
					content: "";
					transition: all 0.4s ease;
					-webkit-transition: all 0.4s ease;
					background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNCIgaGVpZ2h0PSIxNCIgdmlld0JveD0iMCAwIDMyIDMyIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9ImJsYWNrIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMiIgZD0iTTMwIDEyTDE2IDI0TDIgMTIiLz48L3N2Zz4=");
				}
				&:hover:before {
					transform: rotateX(180deg);
				}
			}
			.sub-menu {
				position: absolute;
				top: 15px;
				z-index: 999999;
				display: none;
				width: 258px;
				padding: 25px 0 0;
				opacity: 0;
				visibility: hidden;
				li {
					display: block;
					padding: 0 10px;
					background-color: var(--dark);
					&:first-child a {
						padding-top: 15px;
					}
					&:last-child a {
						padding-bottom: 15px;
					}
					a {
						color: var(--white);
						padding: 0 0 6px 0;
						&:hover {
							color: var(--primary);
						}
					}
				}
			}
			&:hover>.sub-menu {
				display: block;
				opacity: 1;
				visibility: visible;
				-webkit-animation-name: submenu;
				-webkit-animation-duration: 0.5s;
				animation-name: submenu;
				animation-duration: 0.5s;
			}
		}
	}
}
.open-menu {
	position: relative;
	z-index: 9;
	display: inline-flex;
	width: 34px;
	height: 34px;
	padding: 0;
	margin: 0;
	background-color: transparent;
	background-position: 50% 50%;
	background-repeat: no-repeat;
	background-size: 34px;
	border: 0;
	border-radius: 0;
	outline: 0 !important;
	align-items: center;
	justify-content: center;
	-webkit-appearance: none;
	box-shadow: none;
	border: 0;
	&:focus, &:hover, &:active {
		background-color: transparent;
		-webkit-box-shadow: none;
		box-shadow: none;
	}
	&::before,
	&::after {
		content: '';
		position: absolute;
		background: var(--gray);
		width: 100%;
		height: 2px;
		left: 0;
		-webkit-transition: all .28s ease;
		transition: all .28s ease;
	}
	&::before {
		top: 12px;
		transform: rotate(0deg);
	}
	&::after {
		bottom: 12px;
		transform: rotate(0deg);
	}
	span {
		height: 2px;
		width: 100%;
		display: block;
		background: var(--gray);
		-webkit-transition: all .2s ease;
		transition: all .2s ease;
	}
	&.active, &:active {
		-webkit-box-shadow: none;
		box-shadow: none;
	}
}
.admin-bar .site-header {
	//margin-top: 30px;
}
/*========================================= 
// page title área 
=========================================*/
#site-headline {
	padding: 80px 0 50px;
}