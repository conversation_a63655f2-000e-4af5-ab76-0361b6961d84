<?php

function theme_user_additions_scripts() {
  $pluginLocation = plugin_dir_path(__FILE__);
  $translation_array = array(
    'Problem' => __("Problem", THEME_NS) ,
    'templateUrl' => get_theme_file_uri() ,
    'siteUrl' => site_url()
  );
  wp_localize_script('theme-ns-js', 'localizations', $translation_array);
  wp_enqueue_script('theme-ns-js');
}
add_action('wp_enqueue_scripts', 'theme_user_additions_scripts');
function theme_user_avatar_field($user) { ?>
<?php wp_enqueue_media(); ?>

<script type="text/javascript">
	jQuery(document).ready(function($) {
		var custom_uploader;
		$('#upload_image_button').click(function(e) {

			e.preventDefault();
			if (custom_uploader) {
				custom_uploader.open();
				return;
			}
			custom_uploader = wp.media.frames.file_frame = wp.media({
				title: '<?php _e("Set image", THEME_NS); ?>',
				button: {
					text: '<?php _e("Change image", THEME_NS); ?>'
				},
				multiple: false
			});
			custom_uploader.on('select', function() {
				attachment = custom_uploader.state().get('selection').first().toJSON();
				$('#upload_image').val(attachment.id);
				$('#theme_user_image').attr('src', attachment.url);
			});
			custom_uploader.open();

		});

	});

</script>

<hr class="mt40"><h3><?php _e("User picture", THEME_NS); ?></h3>
<table>
	<tr>
		<td>
			<label for='user_attachment_id'><?php _e("Image/Photo: ", THEME_NS); ?></label>
		</td>
		<td>
			<label for='upload_image'>
				<input id="upload_image" type="hidden" size="36" name="ad_image" value="<?php echo get_user_meta($user->ID, 'user_attachment_id', true); ?>" />
				<input id="upload_image_button" class="button" type="button" value="<?php _e("Upload Image", THEME_NS); ?>" />
			</label>
		</td>
		<td><img src="<?php echo wp_get_attachment_url(get_user_meta($user->ID, 'user_attachment_id', true)); ?>" id="theme_user_image" style="height:100px; width: auto;" /></td>
	</tr>
	<?php if (get_user_meta($user->ID, 'user_attachment_id', true) != "") { ?>
	<tr>
		<td><label for='image_user_avatar_remove'><?php _e("Remove image:", THEME_NS); ?></label></td>
		<td colspan='2'><input type='checkbox' value='1' name='user_attachment_id_remove' id='user_attachment_id_remove'>
		</td>
	</tr>
	<?php
  } ?>
</table>
<?php
}
add_action('show_user_profile', 'theme_user_avatar_field');
add_action('edit_user_profile', 'theme_user_avatar_field');
function theme_user_save_avatar_field($user_id) {
  if (!current_user_can('edit_user', $user_id)) {
    return false;
  }
  update_user_meta($user_id, 'user_attachment_id', $_POST['ad_image']);
  if (isset($_POST['user_attachment_id_remove'])) {
    if ($_POST["user_attachment_id_remove"] == 1) {
      update_user_meta($user_id, 'user_attachment_id', "");
    }
  }
}
add_action('personal_options_update', 'theme_user_save_avatar_field');
add_action('edit_user_profile_update', 'theme_user_save_avatar_field');
function theme_user_gravatar_filter($avatar, $id_or_email, $size, $default, $alt) {
  $custom_avatar = get_the_author_meta('user_attachment_id', $id_or_email);
  if ($custom_avatar) $return = theme_user_get_wp_user_avatar_image($id_or_email, $size, $default, $alt);
  elseif ($avatar) $return = $avatar;
  else $return = '<img src="' . $default . '" width="' . $size . '" height="' . $size . '" alt="' . $alt . '" class="avatar avatar-' . $size . '" />';
  return $return;
}
add_filter('get_avatar', 'theme_user_gravatar_filter', 10, 5);

function theme_user_get_wp_user_avatar_image($id_or_email = "", $size = '96', $align = "", $alt = "", $email = '<EMAIL>') {
  global $avatar_default, $blog_id, $post, $wpdb, $_wp_additional_image_sizes;
  if (is_object($id_or_email)) {
    if ($id_or_email->user_id != 0) {
      $email = $id_or_email->user_id;
    }
    elseif (!empty($id_or_email->comment_author_email)) {
      $user = get_user_by('email', $id_or_email->comment_author_email);
      $email = !empty($user) ? $user->ID : $id_or_email->comment_author_email;
    }
    $alt = $id_or_email->comment_author;
  }
  else {
    if (!empty($id_or_email)) {
      $user = is_numeric($id_or_email) ? get_user_by('id', $id_or_email) : get_user_by('email', $id_or_email);
    }
    else {
      $author_name = get_query_var('author_name');
      if (is_author()) {
        $user = get_user_by('slug', $author_name);
      }
      else {
        $user_id = get_the_author_meta('ID');
        $user = get_user_by('id', $user_id);
      }
    }
    if (!empty($user)) {
      $email = $user->ID;
      $alt = $user->display_name;
    }
  }
  $wpua_meta = get_the_author_meta('user_attachment_id', $email);
  $alignclass = !empty($align) && ($align == 'left' || $align == 'right' || $align == 'center') ? ' align' . $align : ' alignnone';
  if (!empty($wpua_meta)) {
    $get_size = is_numeric($size) ? array(
      $size,
      $size
    ) : $size;
    $wpua_image = wp_get_attachment_image_src($wpua_meta, "thumbnail");
    $dimensions = is_numeric($size) ? ' width="' . $size . '" height="' . $size . '"' : "";
    $avatar = '<img src="' . $wpua_image[0] . '"' . $dimensions . ' alt="' . $alt . '" class="avatar avatar-' . $size . '" />';
  }
  else {
    if (!function_exists('get_intermediate_image_sizes')) {
      require_once (ABSPATH . 'wp-admin/includes/media.php');
    }
    $all_sizes = array_merge(get_intermediate_image_sizes() , array(
      'original'
    ));
    if (in_array($size, $all_sizes)) {
      if (in_array($size, array(
        'original',
        'large',
        'medium',
        'thumbnail'
      ))) {
        $get_size = ($size == 'original') ? get_option('large_size_w') : get_option($size . '_size_w');
      }
      else {
        $get_size = $_wp_additional_image_sizes[$size]['width'];
      }
    }
    else {
      $get_size = $size;
    }
    $avatar = get_avatar($email, $get_size, $default = "", $alt = "");
    if (in_array($size, array(
      'original',
      'large',
      'medium',
      'thumbnail'
    ))) {
      $avatar = preg_replace('/(width|height)="d*"s/', "", $avatar);
      $avatar = preg_replace("/(width|height)='d*'s/", "", $avatar);
    }
    $str_replacemes = array(
      'wp-user-avatar ',
      'wp-user-avatar-' . $get_size . ' ',
      'wp-user-avatar-' . $size . ' ',
      'avatar-' . $get_size,
      'photo'
    );
    $str_replacements = array(
      "",
      "",
      "",
      'avatar-' . $size,
      'wp-user-avatar wp-user-avatar-' . $size . $alignclass . ' photo'
    );
    $avatar = str_replace($str_replacemes, $str_replacements, $avatar);
  }
  return $avatar;
}
function theme_user_shortcode() {
  $toReturn = "";
  $userId = get_current_user_id();
  if ($userId > 0) {
    $custom_avatar = get_the_author_meta('user_attachment_id', $userId);
    if ($custom_avatar) {
      $cai = wp_get_attachment_image_src($custom_avatar, 'thumbnail');
      $showAvatar = true;
    }
    else {
      $cai[0] = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8Xw8AAoMBgDTD2qgAAAAASUVORK5CYII=";
      $showAvatar = false;
    }
    $toReturn .= "
				<div id='theme_user_avatar_wrap' style='" . (($showAvatar) ? "" : "display: none") . "'>
					<a href='#_' class='btn btn-outline-danger theme_user_remove'>" . __("Remove", THEME_NS) . "</a>
					<div class='theme_user_avatar'><img src='" . $cai[0] . "'></div>
				</div>
			";
    $toReturn .= '
				<div class="theme_user_upload" style="">
					<form type="post" action="" id="theme_user_submitFileForm" class="" enctype="multipart/form-data">
						<div class="form-group">
							<label for="inputFile">' . __("Select", THEME_NS) . '</label>
							<input type="file" id="inputFile" name="inputFile">
						</div>
					</form>
				
					<button type="button" class="btn btn-primary theme_user_submitFile">' . __("Submit", THEME_NS) . '</button>
				</div>
			';
  }
  return $toReturn;
}
add_shortcode('mb-simple-user-avatar', 'theme_user_shortcode');
function theme_user_remove() {
  $toReturn["success"] = false;
  if (get_current_user_id() > 0) {
    update_user_meta(get_current_user_id() , 'user_attachment_id', "");
    $toReturn["success"] = true;
  }
  echo json_encode($toReturn);
  exit();
}
add_action('wp_ajax_theme_user_remove', 'theme_user_remove');
add_action('wp_ajax_nopriv_theme_user_remove', 'theme_user_remove');
add_action('wp_ajax_nopriv_theme_user_submitFile', 'theme_user_submitFile');
add_action('wp_ajax_theme_user_submitFile', 'theme_user_submitFile');
function theme_user_submitFile() {
  $toReturn["userId"] = get_current_user_id();
  $toReturn["status"] = "";
  if ($toReturn["userId"] > 0) {
    $upload_dir = wp_upload_dir();
    $target_dir = $upload_dir["path"];
    $target_file = $target_dir . "/" . time() . "." . basename($_FILES["inputFile"]["name"]);
    $target_url = $upload_dir["url"] . "/" . time() . "." . basename($_FILES["inputFile"]["name"]);
    $toReturn["validUpload"] = true;
    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
    if (isset($_POST["action"])) {
      $check = getimagesize($_FILES["inputFile"]["tmp_name"]);
      if ($check !== false) {
      }
      else {
        $toReturn["validUpload"] = false;
      }
    }
    if ($toReturn["validUpload"]) {
      if (move_uploaded_file($_FILES["inputFile"]["tmp_name"], $target_file)) {
        $image = $target_url;
        $media = media_sideload_image($image, 0, null, 'id');
        if (!empty($media) && !is_wp_error($media)) {
          update_user_meta($toReturn["userId"], 'user_attachment_id', $media);
          $custom_avatar = get_the_author_meta('user_attachment_id', $toReturn["userId"]);
          $cai = wp_get_attachment_image_src($custom_avatar, 'thumbnail');
          $toReturn["status"] = "success";
          $toReturn["imgUrl"] = $cai[0];
        }
        else {
          $toReturn["media"] = $media;
          $toReturn["wp_error"] = is_wp_error($media);
          $toReturn["status"] = "warning";
          $toReturn["msg"] = "WP error.";
        }
        unlink($target_file);
      }
      else {
        $toReturn["status"] = "warning";
        $toReturn["msg"] = "We could not upload your image.";
      }
    }
    else {
      $toReturn["status"] = "warning";
      $toReturn["msg"] = "File is not an image.";
    }
  }
  echo json_encode($toReturn);
  exit();
}

