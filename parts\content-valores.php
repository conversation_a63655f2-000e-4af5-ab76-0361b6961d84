<section id="valores" class="pt80 pb80 bg-black text-white">
	<div class="inner">
		<div class="g mdoff2">
			<div class="text-balance text-center"><?php echo apply_filters( 'the_content', get_post_meta(get_the_ID(), 'conheca', true));?></div>
		</div>
		<div class="text-center mt40" id="planos-opt">
			<button id="planos-mensais" class="btn btn-plano" onclick="planoMensal()">Mensal</button>
			<button id="planos-anuais" class="btn btn-plano" onclick="planoAnual()">Anual</button>
		</div>
		<div class="g md4 sm2 cg2 rg3 mt20">
			<?php 
				$args = array(
				'post_type' => 'valor',
				'posts_per_page' => -1,
				'orderby' => 'date',
				'post_status'=>'publish',
				'order' => 'ASC',
			);
		$vl = new WP_Query($args);
		if($vl->have_posts()):while($vl->have_posts()) : $vl->the_post(); ?>
			<div class="plano">
				<h3><?php the_title();?></h3>
				<div class="plano-content check"><?php echo str_replace(array('data-list="bullet"', '<p><strong>'), array('', '<p class="sub-title"><strong>'), wpautop(get_the_content())); ?></div>

				<div class="adquirir-plano">
					<?php if (get_post_meta(get_the_ID(), 'valor_mensal', true)){ ?><div class="valor-mensal"><?php echo get_post_meta(get_the_ID(), 'valor_mensal', true); ?><span>/mês</span></div><?php } ?>
					<?php if (get_post_meta(get_the_ID(), 'valor_anual', true)){ ?><div class="valor-anual"><?php echo get_post_meta(get_the_ID(), 'valor_anual', true); ?><span>/anual</span></div><?php } ?>
					<?php if (get_post_meta(get_the_ID(), 'link_compra', true)){ ?><div class="text-center pt20"><a href="<?php echo get_post_meta(get_the_ID(), 'link_compra', true); ?>" target="_blank" class="btn btn-primary" aria-label="adquirir plano <?php the_title();?>">Comprar</a></div><?php } ?>
				</div>
			</div>
			<?php endwhile; endif;wp_reset_postdata(); ?>
		</div>
	</div>
</section>


<script>
	function planoMensal() {
		document.body.classList.remove('plano-anual');
		document.body.classList.add('plano-mensal');
	}

	function planoAnual() {
		document.body.classList.add('plano-anual');
		document.body.classList.remove('plano-mensal');
	}
	setTimeout(function() {
		planoMensal()
	}, 1000);

</script>
