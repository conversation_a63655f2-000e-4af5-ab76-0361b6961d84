<?php

/*
Plugin Name: Theme Base Fields Metabox
Description: Base fields for meta boxes
Version: 1.0
Author: <PERSON>
Author URI:  https://elton.disner.com.br
*/

// Classe ThemeFieldsMetabox
class ThemeFieldsMetabox {
	private $title;
	private $screens;
	private $fields;
	private $isprefix;
	private $position;
	private $priority;
	private $postid;


	public function __construct( $title, $screens, $fields, $isprefix, $position, $priority, $postid ) {
		$this->title    = $title;
		$this->screens  = $screens;
		$this->fields   = $fields;
		$this->isprefix = $isprefix;
		$this->position = $position;
		$this->priority = $priority;
		$this->postid   = $postid;


		add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
		add_action( 'admin_footer', array( $this, 'admin_footer' ) );
		add_action( 'save_post', array( $this, 'save_post' ) );
	}

	public function add_meta_boxes() {
		$post_id = isset( $_GET['post'] ) ? $_GET['post'] : ( isset( $_POST['post_ID'] ) ? $_POST['post_ID'] : '' );

		if ( ! empty( $this->postid ) && ( $post_id == $this->postid || $this->postid == 'any' ) ) {
			foreach ( $this->screens as $screen ) {
				add_meta_box(
					$this->isprefix . '-metabox',
					__( $this->title, $this->isprefix ),
					array( $this, 'add_meta_box_callback' ),
					$screen,
					$this->position,
					$this->priority
				);
			}
		}
	}


	public function add_meta_box_callback( $post ) {
		wp_nonce_field( $this->isprefix . '_metabox_data', $this->isprefix . '_metabox_nonce' );
		$this->generate_fields( $post );
	}

	public function admin_footer() {
		?>
		<style>
			img[src=""], .hidden-img img { opacity: 0; visibility: hidden; display: none; }
			.direction-rtl { direction: rtl } input#link-video { direction: rtl; }
			img#img-intro-img { background-color: #f2f2f2; }
			.editor200 textarea.wp-editor-area { min-height: 200px; }
			.editor300 textarea.wp-editor-area { min-height: 300px; }
			.img40 img { height: 40px; width: auto }
			.img80 img { height: 80px; width: auto }
			#wp-intro_text-media-buttons { display: none; }
			.grid-media { display: grid; grid-template-columns: auto 80px; align-items: center; }
			/*.overf .mce-container>div {white-space: nowrap;overflow: hidden;}*/
		</style>
		<script>
			jQuery(document).ready(function ($) {
				if (typeof wp.media !== 'undefined') {
					var _custom_media = true,
						_orig_send_attachment = wp.media.editor.send.attachment;
					$('.isprefix_metabox-metabox-media').click(function (e) {
						var send_attachment_bkp = wp.media.editor.send.attachment;
						var button = $(this);
						var id = button.attr('id').replace('_button', '');
						_custom_media = true;
						wp.media.editor.send.attachment = function (props, attachment) {
							if (_custom_media) {
								$("#" + id).val(attachment.url);
								$('#img-' + id).attr('src', attachment.url);
							} else {
								return _orig_send_attachment.apply(this, [props, attachment]);
							}
							;
						}
						wp.media.editor.open(button);
						return false;
					});
					$('.add_media').on('click', function () {
						_custom_media = false;
					});
				}
			});
			/* =========================================================== */
			if (document.body.classList.contains('page2')) {
				document.querySelector('#intro_page-metabox').remove();
			}
		</script>
		<?php
	}

	public function generate_fields( $post ) {
		$output = '';
		foreach ( $this->fields as $field ) {
			$db_value = get_post_meta( $post->ID, $field['id'], true );
			$input    = $this->generate_field_input( $field, $db_value );
			$output   .= $this->row_format( $input );
		}
		echo '<div class="g pb15">' . $output . '</div>';
	}

	private function generate_field_input( $field, $db_value ) {
		switch ( $field['type'] ) {
			case 'html':
				return sprintf( '%s', $field['class'] );

			case 'row':
				return sprintf( '</div><div class="%s" data-div="row">', $field['class'] );

			case 'checkbox':
				return sprintf(
					'<div class="%s"><label class="%s checkbox middle pr10" for="%s">%s</label><input %s id="%s" name="%s" type="checkbox" value="1"></div>',
					$field['class'],
					$field['id'],
					$field['id'],
					$field['label'],
					$db_value === '1' ? 'checked' : '',
					$field['id'],
					$field['id']
				);

			case 'media':
				return sprintf(
					'<div class="%s"><label class="%s" for="%s">%s</label><div class="col100 bg-gray"><img src="%s" id="img-%s" alt="img" class="col100"></div><div class="grid-media"><div class="pr0"><input class="regular-text direction-rtl" id="%s" name="%s" type="text" value="%s"></div><div class="pl0"><input class="button isprefix_metabox-metabox-media btn col100" id="%s_button" name="%s_button" type="button" value="Upload" /></div></div></div>',
					$field['class'],
					$field['id'],
					$field['id'],
					$field['label'],
					$db_value,
					$field['id'],
					$field['id'],
					$field['id'],
					$db_value,
					$field['id'],
					$field['id']
				);

			case 'radio':
				$input = '<div class="' . $field['class'] . '"><label class="label-radio" for="' . $field['id'] . '">' . $field['label'] . '</label><br>';
				$i     = 0;
				foreach ( $field['options'] as $key => $value ) {
					$field_value = ! is_numeric( $key ) ? $key : $value;
					$input       .= sprintf(
						'<label class="radio"><input %s id="%s" name="%s" type="radio" class="radio" value="%s"> %s</label>%s',
						$db_value === $field_value ? 'checked' : '',
						$field['id'],
						$field['id'],
						$field_value,
						$value,
						$i < count( $field['options'] ) - 1 ? ' &nbsp; ' : ''
					);
					$i ++;
				}

				return $input . '</div>';

			case 'select':
				$input = sprintf(
					'<div class="%s"><label class="%s" for="%s">%s</label><select id="%s" name="%s">',
					$field['class'],
					$field['id'],
					$field['id'],
					$field['label'],
					$field['id'],
					$field['id']
				);
				foreach ( $field['options'] as $key => $value ) {
					$field_value = ! is_numeric( $key ) ? $key : $value;
					$input       .= sprintf(
						'<option %s value="%s">%s</option>',
						$db_value === $field_value ? 'selected' : '',
						$field_value,
						$value
					);
				}

				return $input . '</select></div>';

			case 'textarea':
				return sprintf(
					'<div class="%s"><label class="%s" for="%s">%s</label><textarea class="large-text" id="%s" name="%s" rows="5">%s</textarea></div>',
					$field['class'],
					$field['id'],
					$field['id'],
					$field['label'],
					$field['id'],
					$field['id'],
					$db_value
				);

			case 'wpeditorbasic':
				ob_start();
				echo '<div class="' . $field['class'] . '"><label class="' . $field['id'] . '" for="' . $field['id'] . '">' . $field['label'] . '</label>';
				wp_editor(
					$db_value,
					$field['id'],
					array(
						'textarea_name' => $field['id'],
						'media_buttons' => false,
						'teeny'       => true,
						'tinymce'     => true,
						'quicktags'   => true,
						'textarea_rows' => 10,
					)
				);
				echo '</div>';

				return ob_get_clean();

			case 'wpeditor':
				ob_start();
				echo '<div class="' . $field['class'] . '"><label class="' . $field['id'] . '" for="' . $field['id'] . '">' . $field['label'] . '</label>';
				wp_editor(
					$db_value,
					$field['id'],
					array(
						'textarea_name' => $field['id'],
						'media_buttons' => false,
						'teeny'       => false,
						'tinymce'     => true,
						'quicktags'   => true,
						'textarea_rows' => 10,
					)
				);
				echo '</div>';

				return ob_get_clean();

			case 'wpeditorfull':
				ob_start();
				echo '<div class="' . $field['class'] . '"><label class="' . $field['id'] . '" for="' . $field['id'] . '">' . $field['label'] . '</label>';
				wp_editor(
					$db_value,
					$field['id'],
					array(
						'textarea_name' => $field['id'],
						'media_buttons' => true,
						'teeny'       => false,
						'tinymce'     => true,
						'quicktags'   => true,
						'textarea_rows' => 10,
					)
				);
				echo '</div>';

				return ob_get_clean();

			default:
				return sprintf(
					'<div class="%s"><label class="%s" for="%s">%s</label><input %s id="%s" name="%s" type="%s" value="%s"></div>',
					$field['class'],
					$field['id'],
					$field['id'],
					$field['label'],
					$field['type'] !== 'color' ? 'class="regular-text"' : '',
					$field['id'],
					$field['id'],
					$field['type'],
					$db_value
				);
		}
		
	}

	public function row_format( $input ) {
		return sprintf( ' %s ', $input );
	}

	public function save_post( $post_id ) {
		// Verify nonce
		if ( ! isset( $_POST[ $this->isprefix . '_metabox_nonce' ] ) || ! wp_verify_nonce( $_POST[ $this->isprefix . '_metabox_nonce' ], $this->isprefix . '_metabox_data' ) ) {
			return $post_id;
		}

		// Check autosave
		if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
			return $post_id;
		}

		// Check user permissions
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return $post_id;
		}

		foreach ( $this->fields as $field ) {
			if ( isset( $_POST[ $field['id'] ] ) ) {
				$value = $_POST[ $field['id'] ];

				switch ( $field['type'] ) {
					case 'email':
						$value = sanitize_email( $value );
						break;
					case 'text':
						$value = sanitize_text_field( $value );
						break;
					// Add more sanitization cases as needed
				}

				update_post_meta( $post_id, $field['id'], $value );
			} elseif ( $field['type'] === 'checkbox' ) {
				update_post_meta( $post_id, $field['id'], '0' );
			}
		}
	}
}



/*=========================================
//		
=========================================*/
function theme_metabox_after_title_meta_boxes() {
	global $post, $wp_meta_boxes;
	do_meta_boxes( get_current_screen(), 'display_after_title', $post );
}
add_action( 'edit_form_after_title', 'theme_metabox_after_title_meta_boxes', 99 );