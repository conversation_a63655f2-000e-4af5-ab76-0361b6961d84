<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Gerador de favicons</title>
	<meta http-equiv="refresh" content="3; URL='https://convertio.co/pt/png-ico/'"/>
	<style>body{padding: 40px;font-family: sans-serif}</style>
</head>
<body>
	<?php

	function resizePng($img,$w=32,$h=32,$newName="favicon.png"){

		$source = $img;
		$source = imagecreatefrompng($source);
		$o_w = imagesx($source);
		$o_h = imagesy($source);

		$newImg = imagecreatetruecolor($w, $h);
		imagealphablending($newImg, false);
		imagesavealpha($newImg,true);
		$transparent = imagecolorallocatealpha($newImg, 255, 255, 255, 127);
		imagefilledrectangle($newImg, 0, 0, $w, $h, $transparent);
		imagecopyresampled($newImg, $source, 0, 0, 0, 0, $w, $h, $o_w, $o_h);

		imagepng($newImg, $newName);
		echo 'Imagem ' . $newName . 'criada!<br>';
	}



	resizePng('favicon.png', 16,16, 'favicon-16x16.png');
	resizePng('favicon.png', 32,32, 'favicon-32x32.png');
	//resizePng('favicon.png', 57,57, 'apple-icon-57x57.png');
	//resizePng('favicon.png', 60,60, 'apple-icon-60x60.png');
	//resizePng('favicon.png', 72, 72, 'apple-icon-72x72.png');
	//resizePng('favicon.png', 76,76, 'apple-icon-76x76.png');
	resizePng('favicon.png', 96,96, 'favicon-96x96.png');
	//resizePng('favicon.png', 114,114, 'apple-icon-114x114.png');
	//resizePng('favicon.png', 120,120, 'apple-icon-120x120.png');
	//resizePng('favicon.png', 144,144, 'apple-icon-144x144.png');
	resizePng('favicon.png', 144,144, 'ms-icon-144x144.png');
	//resizePng('favicon.png', 152,152, 'apple-icon-152x152.png');
	resizePng('favicon.png', 180,180, 'apple-icon-180x180.png');
	resizePng('favicon.png', 192,192, 'android-icon-192x192.png');
	resizePng('favicon.png', 270,270, 'favicon-270x270.png');

?>

</body>
</html>