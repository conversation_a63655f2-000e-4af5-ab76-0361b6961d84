aside#intro, #site-headline {
	position: relative;
	min-height: 650px;
	//height: 85dvh;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	font-size: 22px;
	overflow: hidden;
	h1 {
		font-size: 54px;
		font-weight: 200;
		line-height: 1.1;
		color: #fff;
	}
	.btn {
		padding-left: 40px;
		padding-right: 40px;
		background: var(--webkit-gradient);
		background: var(--gradient);
		font-size: 16px;
		border: 0;
		color: var(--white);
		margin-top: 20px;
		&:hover {
			background-color: var(--secondary);
			color: #fff
		}
	}
	.intro-entry {
		position: relative;
		z-index: 3;
		width: 100%;
	}
	.bg.circle {
		position: relative;
		width: 150px;
		height: 150px;
		margin: 0 auto;
		padding-top: 0;
		margin-bottom: 40px;
		border: 2px solid var(--dark);
	}
}
#bg, .absolute.bg {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 0;
}
img.logo-intro {
	width: 500px;
	margin: 0 auto 40px;
}