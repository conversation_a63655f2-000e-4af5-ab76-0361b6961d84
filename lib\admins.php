<?php 

locate_template(array('lib/wp-fields.php') , true);
locate_template(array('lib/metabox.php'), true);

/*=========================================
//		
=========================================*/
function resize_upload_image($file) {
    if (strpos($file['type'], 'image') !== false) {
        $max_width = 1920;
        $image = wp_get_image_editor($file['file']);
        if (!is_wp_error($image)) {
            $image_data = $image->get_size();
            if ($image_data['width'] > $max_width) {
                $image->resize($max_width, null, false);
                $saved_image = $image->save($file['file']);
                if (!is_wp_error($saved_image)) {
                    $file['file'] = $saved_image['path'];
                    $file['url'] = wp_get_attachment_url($file['file']);
                }
            }
        }
    }

    return $file;
}
add_filter('wp_handle_upload', 'resize_upload_image');
/*=========================================
//		
=========================================*/	
add_action( 'add_attachment', 'change_add_image_meta_data' );
function change_add_image_meta_data( $attachment_ID ) {
	if(isset($_REQUEST['name'])){
		 $filename=$_REQUEST['name'];
		 $post_id = isset($_REQUEST['post_id']) ? $_REQUEST['post_id'] : false;

		 if ($post_id && is_numeric($post_id)) {
			 $post_title = get_the_title($post_id);
			 $title = ($post_title && $post_title != 'Rascunho automático') ? $post_title : get_bloginfo('name');
					wp_update_post(array(
						 'ID'            => $attachment_ID,
						 'post_title'    => $title,
						 'post_excerpt'  => '',
						 'post_content'  => $title,
					));
					update_post_meta($attachment_ID, '_wp_attachment_image_alt', $title);
		 }
	}
}


/*=========================================
//		
=========================================*/
function remove_exif_metadata($file) {
    $image_info = @exif_read_data($file);
    if (!$image_info) {
        return $file; // Evita processamento desnecessário
    }

    $image = wp_get_image_editor($file);
    if (!is_wp_error($image)) {
        $image->save($file);
    }

    return $file;
}
add_filter('wp_handle_upload', function ($file) {
    if (!empty($file['file']) && preg_match('/\.(jpe?g|png)$/i', $file['file'])) {
        $file['file'] = remove_exif_metadata($file['file']);
    }
    return $file;
});


/*=========================================
//		image send editor
=========================================*/
add_filter('image_send_to_editor', 'addDecodingAsync');
function addDecodingAsync($html) {

	$str = ['class="','<img '];
	$rplc =['data-class="','<img decoding="async" loading=lazy '];
	
  return str_replace($str,$rplc,$html);
}

/*=========================================
//		Remove itens admin bar
=========================================*/
function alterar_admin_bar( $admin_bar ) {
 
    $admin_bar->remove_menu( 'wp-logo' );
    $admin_bar->remove_menu( 'updates' );
    $admin_bar->remove_menu( 'search' );
    $admin_bar->remove_menu( 'comments' );
 
    return $admin_bar;
 
}
add_action( 'admin_bar_menu', 'alterar_admin_bar', 99 );

/*=========================================
//		remove dashboard widgets
=========================================*/
function remove_dashboard_meta() {
        remove_meta_box( 'dashboard_incoming_links', 'dashboard', 'normal' );
        remove_meta_box( 'dashboard_plugins', 'dashboard', 'normal' );
        remove_meta_box( 'dashboard_secondary', 'dashboard', 'normal' );
        remove_meta_box( 'dashboard_quick_press', 'dashboard', 'side' );
        remove_meta_box( 'dashboard_right_now', 'dashboard', 'normal' );
        remove_meta_box( 'dashboard_activity', 'dashboard', 'normal');//since 3.8
        remove_meta_box( 'woocommerce_dashboard_status', 'dashboard', 'normal');
        remove_meta_box( 'woocommerce_dashboard_recent_reviews', 'dashboard', 'normal');
        remove_meta_box( 'dashboard_primary', 'dashboard', 'side' );
        remove_meta_box( 'dashboard_recent_drafts', 'dashboard', 'side' );
        remove_meta_box( 'dashboard_recent_comments', 'dashboard', 'normal' );
}
add_action( 'admin_init', 'remove_dashboard_meta' );
/*=========================================
//		
=========================================*/

class Theme_Default_Author{
    const SETTING = 'theme_default_author';
    const SECTION = 'default';
    const PAGE = 'writing';

    public static function init(){
        add_action('admin_init', array('Theme_Default_Author', 'settings'));
        add_filter('wp_insert_post_data', array('Theme_Default_Author', 'change_author'));
    }

    public static function settings(){
        register_setting(self::PAGE, self::SETTING, array('Theme_Default_Author', 'clean_cb'));
        add_settings_field('theme-default-author', __('Default Author', 'default-author'), array('Theme_Default_Author', 'field_cb'), self::PAGE, self::SECTION);
    }

    public static function clean_cb($in){
        return absint($in);
    }

    public static function field_cb(){
        $users = get_users(array('orderby'=> 'nicename'));
        printf('<select id="%1$ss" name="%1$s">', esc_attr(self::SETTING));
        foreach($users as $u){
            printf('<option value="%s" %s>%s</option>', absint($u->ID), selected(absint($u->ID), self::opt(), false), esc_html($u->display_name));
        }
        echo '</select>';
    }

    public static function change_author($arr){
        if(!($aid = self::opt())){
            return $arr;
        }
        $arr['post_author'] = $aid;
        return $arr;
    }

    protected static function opt(){
        return get_option(self::SETTING, 0);
    }
}

/*=========================================
//		
=========================================*/
$current_user = wp_get_current_user();
if ( get_site_option( 'theme_name_default' ) == $current_user->user_login ) {
/* =================================================
 //  
==================================================*/
Theme_Default_Author::init();
} else {
	
	add_filter( 'views_plugins', 'mu_remove_mu_plugins' );
	add_filter( 'show_advanced_plugins', 'mu_hide_advanced_plugins', 10, 2 );
	
	function mu_remove_mu_plugins( $views ) {
		if ( isset( $views[ 'mustuse' ] ) )
			unset( $views[ 'mustuse' ] );
		return $views;
	}

	function mu_hide_advanced_plugins( $default, $type ) {
		if ( $type == 'mustuse' ) return false;
		return $default;
	}
}
/*=========================================
//		Dashboard full widget
=========================================*/
add_action( 'admin_footer', 'theme_custom_dashboard_widget' );
function theme_custom_dashboard_widget() {
	if ( get_current_screen()->base !== 'dashboard' ) {
		return;
	}
?>
<div id="theme-welcome">
	<div id="theme-welcome-panel">
		<div class="welcome-panel-content">
			<h2>Olá usuário!</h2>
			<p class="about-description mt0">Nós montamos alguns links para você começar:</p>
			<div class="g cg4 rg4">
				<div class="md5 sm5 v-top">
					<h3>Conteúdos</h3>
					<div class="col100" style="margin-bottom: 10px">
						<div class="pt10 g cg0">
							<a class="btn md6 sm6" href="<?php echo home_url(); ?>/wp-admin/edit.php">Publicações</a>
							<a class="btn md6 sm6" href="<?php echo home_url(); ?>/wp-admin/edit.php?post_type=page">Paginas</a>
						</div>
					</div>
					<div class="col100">
						<?php $args = array('_builtin' => false, 'show_ui' => true );$post_types = get_post_types( $args, 'objects' );
						unset( $post_types['leads'] );
						unset( $post_types['wpcf7_contact_form'] );
						// Ordena os post_types alfabeticamente pelo nome
						ksort($post_types);
						$i=1;echo '<div class="g cg0 rg0">';
						foreach ( $post_types  as $post_type ) {
							 echo '<div class="btn bg-gray md4 sm4"><a href="'.home_url().'/wp-admin/edit.php?post_type='.$post_type->rewrite['slug'].'" class="dashicons-before '.$post_type->menu_icon.'"><span class="pl5 v-bottom font-12">' . $post_type->labels->menu_name . "</span></a></div>";
							if($i % 3 == 0) {echo '</div><div class="g cg0 rg0 pt5">';}$i++;
						}
						echo '</div><style>.btn.bg-gray .dashicons-before:before,.btn.bg-gray .dashicons-images-alt2:before {vertical-align: middle;}</style>';
					?>
					</div>
				</div>
				<div class="md4 sm4 v-top">
					<h3>Informações do site</h3>
					<div class="g">
						<div class="pt10 md8"><a class="btn button button-hero col100 mb0" href="<?php echo home_url(); ?>/wp-admin/customize.php">Dados do site</a></div>
					</div>
					<p style="font-size: 12px;">Dados como telefone, e-mail, endereço, links das redes sociais. <br>Códigos de rastreamento e Google Analytics</p>
					<ul style="padding-bottom: 15px; display: none">
						<li><a href="<?php echo home_url(); ?>/wp-admin/edit.php?post_type=slide" class="wp-menu-image dashicons-before dashicons-images-alt2"> Editar slides da home</a></li>
						<li><a href="<?php echo home_url(); ?>/wp-admin/admin.php?page=wpcf7db" class="welcome-icon"><span class="dashicons dashicons-email-alt"></span> Ver mensagens de e-mail</a></li>
						<li><a href="<?php echo home_url(); ?>/wp-admin/post-new.php" class="welcome-icon welcome-write-blog">Editar notícias</a></li>
					</ul>
				</div>
				<div class="md3 sm3 xs12 v-top">
					<h3>Mais ações</h3>
					<ul>
						<li>
							<div><a href="<?php echo home_url(); ?>/wp-admin/profile.php"><span class="dashicons dashicons-admin-users"></span> Meu Perfil</a></div>
						</li>
						<li><a href="<?php echo home_url(); ?>/wp-admin/users.php" class=""><span class="dashicons dashicons-id"></span> Usuários</a></li>
						<li><a href="<?php echo home_url(); ?>/" rel="noopener" target="_blank"><span class="dashicons dashicons-admin-home"></span> Ver o site →</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	document.querySelector('h1').insertAdjacentHTML('afterend', document.getElementById('theme-welcome').innerHTML);
</script>
<style>
	#welcome-panel, #theme-welcome {display: none !important;}
</style>
<?php }


/*=========================================
//		Script ajax cache
=========================================*/	
add_filter('admin_footer_text', 'add_script_footer_cache_cleaner');
function add_script_footer_cache_cleaner(){ ?>
<script>
	const admin_url = "<?php echo admin_url('admin-ajax.php'); ?>";
	document.querySelectorAll("#wp-admin-bar-limpar-cache a, #customize-save-button-wrapper #save").forEach(item => {
		item.addEventListener('click', event => {
			event.preventDefault();
			var xhttp = new XMLHttpRequest();
			xhttp.open("GET", admin_url + "?action=purge_css_cache", false);
			xhttp.send();
			document.getElementById("wpbody").insertAdjacentHTML("beforeBegin", "<div id=cache-message>" + xhttp.responseText + "</div>");
			setTimeout(function() {
				document.getElementById('cache-message').remove();
			}, 15000);
		})
	});

</script>
<style>
	#cache-message {
		position: fixed;
		right: 2px;
		top: 63px;
		z-index: 4;
		min-width: 328px;
	}

</style>
<?php 
}


/* =================================================
 //  remove h1 from heading
==================================================*/
function remove_h1_from_heading($args) {
$args['block_formats'] = 'Paragraph=p;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Pre=pre';
return $args;
}
add_filter('tiny_mce_before_init', 'remove_h1_from_heading' );
/* =================================================
 //  delete revision
==================================================*/
add_action( 'admin_init', 'add_delete_revision_actions' );
function add_delete_revision_actions () {
    $post_types = get_post_types( array( 'exclude_from_search' => false ) );
    foreach ($post_types as $post_type) {
        if ( post_type_supports( $post_type, 'revisions' )) {
            add_action("publish_$post_type", 'delete_revisions_on_publish', 10, 1);
        }
    }
}
function delete_revisions_on_publish ($post_id) {
    $revisions = wp_get_post_revisions($post_id);
    foreach ($revisions as $revision) {
        $delete = wp_delete_post_revision($revision->ID);
    }
}
/* =================================================
 //  
==================================================*/
add_filter( 'admin_body_class', 'display_admin_body_class' );
function display_admin_body_class( $classes )
{
	if(isset($_GET['post'])) {
	$post_id = $_GET['post'] ? $_GET['post'] : $_POST['post_ID'];
    $screen = get_current_screen();
    if ( 'post' == $screen->base ){
			$postid = get_the_ID();
			if($postid >= 0){
				$post = get_post($postid); 
				$slug = $post->post_name;
        $classes .= ' ' . $screen->post_type . $postid . ' ' . $slug;
    		return $classes;
			}
		}
	}
}

/*==================================*/
//		
/*==================================*/
function theme_custom_add_editor_styles() {
    add_editor_style( get_template_directory_uri().'/css/admin/admin-editor.css' );
}
add_action( 'after_setup_theme', 'theme_custom_add_editor_styles' );
/*=========================================
//	disable auto save
=========================================*/	
add_action( 'admin_init', 'disable_autosave' );
function disable_autosave() {
	wp_deregister_script( 'autosave' );
}

/*=========================================
//	disable save title empty
=========================================*/
add_action('wp_ajax_check-posts-by-title', function(){
    $title = isset( $_REQUEST['title'] ) ? esc_attr( $_REQUEST['title'] ) : null;
    if ( !$title ) {
        return wp_send_json(array(
        'enable_btn' => false,
        'message' => 'No title specified!'
    ));
    }
    $post = get_page_by_title($title, OBJECT, 'post');
        if ( isset($post->ID) && 'publish' === $post->post_status ) {
        return wp_send_json(array(
        'enable_btn' => false
    ));
    }
    return wp_send_json(array(
        'enable_btn' => true
    ));
    wp_die();
});

add_action('admin_footer', function() {
    global $pagenow;
    if ( !$pagenow || !in_array($pagenow, array( 'post-new.php' )) )
    return;
?>
<script>jQuery(document).ready(function($) {$(document).on("change", "#titlediv #title", function(e) {var i = $(this),f = i.closest('form'),t = i.val(),b = $('input[name="publish"]', f).first();if (self.xhr) {self.xhr.abort();} else {var xhr;}if (!b.length) return;b.attr('disabled', 'disabled');if ($.trim(t)) {self.xhr = $.ajax({type: 'post',url: 'admin-ajax.php',data: {action: 'check-posts-by-title',title: t},success: function(res) {if (res && res.enable_btn) {b.removeAttr('disabled');} else {if (res.message) {alert(res.message);}}},error: function() {i.trigger('change');}});}});$('#titlediv #title').trigger('change');});

</script>
<?php
});

/*=========================================
//		
=========================================*/
add_action('admin_notices', 'admin_theme_info_notice');
function admin_theme_info_notice(){
	if ( get_current_screen()->base !== 'dashboard' ) {
		return;
	}
}
add_filter('admin_footer_text', 'bl_admin_footer');
function bl_admin_footer() {
  echo 'Copyright © '.date("Y").' | Feito por <a href="https://elton.disner.com.br" class="text-white" rel="noopener" target="_blank">eQtoon</a>';
}
/*=========================================
//		Change Admin Login URL 
=========================================*/	
add_filter( 'login_url', 'custom_login_url', PHP_INT_MAX );
function custom_login_url( $login_url ) {
	if(is_admin()){
		$login_url = site_url( 'wp-elogin.php', 'login' );	
			return $login_url;
	}
}
/*=========================================
//		save in admin bar
=========================================*/
function custom_save_button_script() {
    global $post;
    if (isset($_GET['action']) && $_GET['action']==='edit' && is_admin()) {
        ?>
        <script>
            jQuery(document).ready(function ($) {
                $('#wp-admin-bar-top-secondary').append('<li id="custom-save-button" class="button-disabled mr10"><a href="#" id="custom-save-action">Salvar/Atualizar</a></li>');
                $('#custom-save-action').on('click', function (e) {
                    e.preventDefault();
                    $('#publish').click();
                });
            });
        </script>
        <?php
    }
}

add_action('admin_footer', 'custom_save_button_script');

