:root {
	--white: #fff;
	--primary: #3c6287;
	--gray: #f6f8fe;
	--dark: #51575d;
	--black: #7f8996;
	--hover: #202324;
	--g2: repeat(2, 1fr);
	--g3: repeat(3, 1fr);
	--g4: repeat(4, 1fr);
	--g5: repeat(5, 1fr);
	--g6: repeat(6, 1fr);
}
* {
	list-style: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
#adminmenu {
	padding: 68px 0 0;
	background: url(../../images/logo.png) 10px 10px/148px no-repeat;
	li, a {
		outline: 0 !important;
	}
}
body {
	font-size: 16px;
	color: #8691b4;
	line-height: 1.5rem;
	background: #F6F8FE;
	overflow-x: hidden;
}
a {
	text-decoration: none
}
h1, h2, h3, h4, h5, h6 {
	margin-bottom: 0.5rem;
	font-weight: 500;
	line-height: 1.2;
	color: #1F2641;
}
.absolute {
	position: absolute
}
#wpadminbar {
	background: #7f8996;
	box-shadow: 0px 5px 10px #7f89967d;
	border-bottm: 1px solid #46494d4d;
}
#wpadminbar .ab-empty-item, #wpadminbar a.ab-item, #wpadminbar>#wp-toolbar span.ab-label, #wpadminbar>#wp-toolbar span.noticon {
	color: var(--white);
}
#wpadminbar .ab-top-menu>li.hover>.ab-item, #wpadminbar.nojq .quicklinks .ab-top-menu>li>.ab-item:focus, #wpadminbar:not(.mobile) .ab-top-menu>li:hover>.ab-item, #wpadminbar:not(.mobile) .ab-top-menu>li>.ab-item:focus {
	background: var(--hover);
	color: var(--white);
}
#wpadminbar .quicklinks .ab-sub-wrapper .menupop.hover>a, #wpadminbar .quicklinks .menupop ul li a:focus, #wpadminbar .quicklinks .menupop ul li a:focus strong, #wpadminbar .quicklinks .menupop ul li a:hover, #wpadminbar .quicklinks .menupop ul li a:hover strong, #wpadminbar .quicklinks .menupop.hover ul li a:focus, #wpadminbar .quicklinks .menupop.hover ul li a:hover, #wpadminbar .quicklinks .menupop.hover ul li div[tabindex]:focus, #wpadminbar .quicklinks .menupop.hover ul li div[tabindex]:hover, #wpadminbar li #adminbarsearch.adminbar-focused:before, #wpadminbar li .ab-item:focus .ab-icon:before, #wpadminbar li .ab-item:focus:before, #wpadminbar li a:focus .ab-icon:before, #wpadminbar li.hover .ab-icon:before, #wpadminbar li.hover .ab-item:before, #wpadminbar li:hover #adminbarsearch:before, #wpadminbar li:hover .ab-icon:before, #wpadminbar li:hover .ab-item:before, #wpadminbar.nojs .quicklinks .menupop:hover ul li a:focus, #wpadminbar.nojs .quicklinks .menupop:hover ul li a:hover, #wpadminbar:not(.mobile)>#wp-toolbar a:focus span.ab-label, #wpadminbar:not(.mobile)>#wp-toolbar li:hover span.ab-label, #wpadminbar>#wp-toolbar li.hover span.ab-label {
	color: #9aaec7;
}
.wp-person a:focus .gravatar, a:focus, a:focus .media-icon img {
	color: var(--dark);
	text-shadow: 0px 0px 10px #7f8996;
	outline: 1px solid #0000;
	box-shadow: none !important;
}
.welcome-panel {
	background: var(--white);
	border-radius: 15px;
	margin-bottom: 30px;
	padding: 20px;
	box-shadow: 0px 5px 20px rgba(55, 55, 89, 0.08);
	border: 0;
	a.dashicons-before {
		display: flex;
		align-items: center;
		column-gap: 5px;
	}
}
#theme-welcome-panel .g.cg0 a {
	display: flex;
	align-items: center;
	column-gap: 4px;
	padding: 0;
}
#screen-meta-links .show-settings:after {
	color: var(--dark)
}
.misc-pub-section {
	padding: 0 0 0 10px;
	font-size: 12px;
}
.spinner {
	width: 16px;
}
.postbox input[type="text"] {
	width: 100%;
}
.options_group.pricing.show_if_simple.show_if_external.hidden {
	background: #6db7da;
	color: var(--white);
	p {
		font-size: 15px;
	}
}
div {
	&#product_catdiv h2.hndle.ui-sortable-handle {
		background: #6db7da;
		color: var(--white);
		border-bottom: 3px solid #eceef3;
	}
	&#postexcerpt h2.hndle.ui-sortable-handle {
		background: #e29625;
		color: var(--white);
		border-bottom: 3px solid #eceef3;
	}
}
#postimagediv .postbox-header {
	background: #d0d5dc;
}
.post-new-php #postimagediv .inside:after {
	content: "Para melhor resultados SEO, salve a publicação como rascunhou ou publique antes que realizar o upload de imagens.";
	font-size: 12px;
	display: block;
	color: #646f7c;
	padding: 5px;
	background: #eceef3;
	border-radius: 3px;
	border: 1px solid #85a85c;
}
.inside {
	select, textarea {
		width: 100%;
	}
}
textarea#frase-do-slide {
	height: 140px;
}
.categorydiv div.tabs-panel, .customlinkdiv div.tabs-panel, .posttypediv div.tabs-panel, .taxonomydiv div.tabs-panel, .wp-tab-panel {
	background: var(--white);
}
.metabox-holder {
	.postbox>h3, .stuffbox>h3, h2.hndle, h3.hndle {
		background: transparent;
	}
}
#adminmenu {
	width: 200px;
	.wp-submenu {
		width: 200px;
		background-color: #eceef3;
	}
}
#adminmenuback, #adminmenuwrap {
	width: 200px;
	background-color: #eceef3;
}
#adminmenu {
	.wp-has-current-submenu .wp-submenu .wp-submenu-head {
		background: #dbe0e8;
		color: #000;
	}
	.wp-menu-arrow {
		background: rgba(220, 221, 222, 0);
		color: #000;
		div {
			background: rgba(220, 221, 222, 0);
			color: #000;
		}
	}
	li {
		&.current a.menu-top, &.wp-has-current-submenu a.wp-has-current-submenu {
			background: rgba(220, 221, 222, 0);
			color: var(--dark);
			font-weight: 800;
		}
	}
}
.folded #adminmenu li {
	&.current.menu-top, &.wp-has-current-submenu {
		background: #dbe0e8;
		color: #000;
	}
}
#adminmenu li {
	a:focus div.wp-menu-image:before, &.opensub div.wp-menu-image:before, &:hover div.wp-menu-image:before, &.menu-top:hover, &.opensub>a.menu-top, >a.menu-top:focus {
		color: var(--dark);
		background: #eceef3;
	}
}
.postbox {
	min-width: 255px;
	overflow: hidden;
	margin-bottom: 15px !important;
	background: var(--white);
	border-radius: 10px;
	margin-bottom: 30px;
	box-shadow: 0px 5px 20px rgba(55, 55, 89, 0.08);
	border: 0;
}
#side-sortables .postbox {
	margin-bottom: 0;
}
a {
	color: var(--dark);
	-webkit-transition: all 0.7s;
	-moz-transition: all 0.7s;
	-o-transition: all 0.7s;
	transition: all 0.7s;
	&:hover {
		color: var(--dark);
	}
}
.wp-core-ui .button-primary {
	&.focus, &.hover, &:focus, &:hover {
		background: var(--dark);
		border-color: var(--dark);
		color: var(--white);
	}
}
.wp-core-ui .button, .wp-core-ui .button-secondary {
	color: var(--dark);
	border-color: var(--dark);
}
#wp-content-editor-tools {
	background-color: #eceef3;
}
#wp-content-wrap * {
	border-color: #eceef3 !important;
}
.wp-core-ui .quicktags-toolbar input.button.button-small {
	-webkit-filter: brightness(.95);
	filter: brightness(.95);
}
.wp-editor-expand #post-status-info {
	border-top: 1px solid #eceef3;
}
#post-status-info {
	border: 1px solid #eceef3;
	box-shadow: 0px 5px 20px rgba(55, 55, 89, 0.08);
}
#titlediv #title {
	padding: 3px 8px;
	font-size: 26px;
	height: 44px;
	border-color: var(--white);
	border-radius: 8px;
	box-shadow: 0px 5px 20px rgba(55, 55, 89, 0.08);
}
#major-publishing-actions {
	margin-top: 10px;
	padding: 10px;
	clear: both;
	border-top: 1px solid var(--dark);
	background: var(--white);
}
.wp-core-ui .button-primary {
	background: var(--dark);
	border-color: var(--dark);
	box-shadow: 0 1px 0 var(--dark);
	color: var(--white);
	text-decoration: none;
}
#post-body ul {
	&.add-menu-item-tabs li.tabs a, &.category-tabs li.tabs a {
		color: var(--dark);
	}
}
#side-sortables {
	.add-menu-item-tabs .tabs a, .category-tabs .tabs a {
		color: var(--dark);
	}
}
#nav-menu-meta label.menu-item-title {
	white-space: nowrap;
	font-size: 14px;
	line-height: 16px;
	margin-bottom: 0;
}
#menu-management .menu-edit, #menu-settings-column .accordion-container, .comment-ays, .feature-filter, .imgedit-group, .manage-menus, .menu-item-handle, .popular-tags, .stuffbox, .widget-inside, .widget-top, .widgets-holder-wrap, .wp-editor-container, p.popular-tags, table.widefat {
	border: 1px solid #ccd0d452;
	box-shadow: 0px 5px 20px rgba(55, 55, 89, 0.08);
	border-radius: 10px;
}
.wp-tab-bar .wp-tab-active a {
	color: var(--dark);
}
.wp-tab-active {
	border: 1px solid #1d222d;
	border-bottom-color: #1d222d;
	background-color: #1d222d;
}
ul {
	&.add-menu-item-tabs li.tabs, &.category-tabs li.tab {
		border: 1px solid var(--gray);
		border-bottom-color: var(--gray);
		background-color: var(--gray);
	}
}
.categorydiv div.tabs-panel, .customlinkdiv div.tabs-panel, .posttypediv div.tabs-panel, .taxonomydiv div.tabs-panel, .wp-tab-panel {
	min-height: 42px;
	max-height: 200px;
	overflow: auto;
	padding: 0 0.9em;
	border: none;
	background-color: #f5f5f5;
}
#wpcontent, #wpfooter {
	margin-left: 200px;
}
.postbox .inside, .stuffbox .inside {
	padding: 0 12px 12px;
	line-height: 100%;
	font-size: 15px;
}
#dashboard-widgets-wrap, #all_views_dashboard_widget, #views_dashboard_widget, #prev_views_dashboard_widget {
	.postbox {
		min-width: 255px;
		overflow: hidden;
		margin-bottom: 15px !important;
		background: var(--white);
		border-radius: 8px;
		margin-bottom: 30px;
		box-shadow: 0px 5px 20px rgba(55, 55, 89, 0.08);
		border: 0;
		padding: 6px 0;
	}
	.metabox-holder {
		.postbox>h3, .stuffbox>h3, h2.hndle, h3.hndle {
			border-bottom: 0;
			background: none;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
		}
	}
}
th#views {
	width: 75px;
	text-align: center;
	a span {
		text-align: center
	}
}
td.views.column-views {
	text-align: center;
}
th#wordcount {
	width: 75px;
}
th#tcb_post_thumb {
	width: 75px;
}
th#taxonomy-destaque {
	width: 100px;
}
th#categories {
	width: 100px;
}
#adminmenu {
	a {
		color: #000;
	}
	.wp-submenu a {
		&:focus, &:hover {
			color: var(--dark) !important;
		}
	}
	a {
		box-shadow: 0px 5px 2px -2px #eceef3;
	}
	a:hover, li.menu-top>a:focus {
		color: var(--dark) !important;
		box-shadow: 0px 2px 2px -2px var(--dark);
	}
	.wp-not-current-submenu .wp-submenu {
		min-width: 180px;
		width: auto;
		background: #eceef3 !important;
	}
}
#adminmenu div.wp-menu-name {
	padding: 8px 0;
	padding: 10px 0px;
	border-radius: 0px;
	font-weight: 500;
}
.folded #adminmenu .wp-has-current-submenu .wp-submenu {
	min-width: 180px;
	width: auto;
	background: #eceef3 !important;
}
#adminmenu .wp-not-current-submenu li>a, .folded #adminmenu .wp-has-current-submenu li>a {
	padding-left: 21px;
}
#adminmenu {
	.wp-submenu-head, a.menu-top {
		font-size: 16px;
		font-weight: 400;
		line-height: 18px;
		padding: 0;
	}
	.wp-has-current-submenu {
		.wp-submenu {
			background: #eceef3 !important;
			border-left: 5px solid #7f89966b !important;
			margin-left: 10px !important;
			&.sub-open {
				background: #eceef3;
			}
		}
		&.opensub .wp-submenu {
			background: #dbe0e8;
		}
	}
	a.wp-has-current-submenu:focus+.wp-submenu {
		background: #dbe0e8;
	}
}
.no-js li.wp-has-current-submenu:hover .wp-submenu {
	background: #dbe0e8;
}
#adminmenu {
	.opensub .wp-submenu li.current a {
		color: #000;
	}
	.wp-submenu li.current {
		color: #000;
		a {
			color: #000;
			&:focus, &:hover {
				color: #000;
			}
		}
	}
	a.wp-has-current-submenu:focus+.wp-submenu li.current a {
		color: #000;
	}
	.wp-submenu {
		a {
			color: #000;
		}
		width: 194px;
	}
}
.wrap {
	.add-new-h2, .page-title-action {
		border: 2px solid var(--dark);
		background: var(--white);
		color: var(--dark);
		border-radius: 5px;
	}
	.add-new-h2:hover, .page-title-action:hover {
		border-color: var(--dark);
		background: var(--dark);
		color: var(--white);
		border-radius: 5px;
	}
}
.wrap .page-title-action:focus {
	border-color: var(--dark);
	background: var(--dark);
	color: var(--white);
	box-shadow: none;
	outline: 0;
}
input[type=checkbox]:focus, input[type=color]:focus, input[type=date]:focus, input[type=datetime-local]:focus, input[type=datetime]:focus, input[type=email]:focus, input[type=month]:focus, input[type=number]:focus, input[type=password]:focus, input[type=radio]:focus, input[type=search]:focus, input[type=tel]:focus, input[type=text]:focus, input[type=time]:focus, input[type=url]:focus, input[type=week]:focus, select:focus, textarea:focus {
	border-color: var(--dark);
	box-shadow: 0 0 0 1px var(--dark);
	outline: 2px solid #0000;
}
.wp-core-ui .button-primary:hover {
	background: var(--white);
	color: var(--dark);
	border-color: var(--dark);
	text-shadow: none;
}
#adminmenu div.wp-menu-image:before {
	color: var(--white);
}
tbody#the-list {
	td {
		border-bottom: 1px solid #ccd0d452;
	}
	th {
		border-bottom: 1px solid #ccd0d452 !important;
	}
}
.alternate, .striped>tbody> :nth-child(odd), ul.striped> :nth-child(odd) {
	background-color: var(--white);
}
tbody#the-list tr {
	-webkit-transition: all 0.7s;
	-moz-transition: all 0.7s;
	-o-transition: all 0.7s;
	transition: all 0.7s;
	&:hover {
		background: #dbe0e8;
	}
}
body {
	background: #eceef3;
	color: #222;
}
#wp-admin-bar-view a.ab-item, #wp-admin-bar-edit a.ab-item {
	background: var(--dark) !important;
}
form.site-info {
	tr {
		display: inline-block;
		width: 50%;
		&:last-child {
			display: block;
			width: 100%;
		}
	}
	th {
		width: 64%;
		&[scope="row"] {
			width: 20%;
			min-width: 80px;
		}
	}
}
input.regular-text, .meta-box-sortables select {
	width: 100%;
	box-sizing: border-box;
	line-height: 34px;
	padding: 0 15px;
	height: 36px;
}
textarea {
	width: 100%;
	height: 100px;
}
li#menu-posts-product {
	color: #c193f3 !important;
	a {
		color: #c193f3 !important;
	}
}
.wp-core-ui .button.button-primary.button-hero {
	box-shadow: none;
}
.wp-core-ui .welcome-panel .button.button-hero {
	margin: 0 0 15px;
	padding: 0px 36px;
	box-shadow: none !important;
	line-height: 44px;
	height: 46px;
	border-radius: 5px !important;
}
#adminmenu li.menu-top {
	border: none;
	min-height: 38px;
	position: relative;
	padding: 0 3px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.18);
}
#adminmenu li.wp-menu-separator, .notice.notice-success.is-dismissible.pum-notice, .category-tabs, a[href="edit-tags.php?taxonomy=formato&post_type=curso"], ul.wp-submenu a[href="options-general.php?page=github-updater"] {
	display: none !important;
}
.wp-core-ui .button-primary {
	&.active {
		background: var(--dark);
		border-color: var(--dark);
		filter: brightness(1.25);
		text-shadow: none !important;
		color: var(--white);
		border-radius: 15px;
		&:focus, &:hover {
			background: var(--dark);
			border-color: var(--dark);
			filter: brightness(1.25);
			text-shadow: none !important;
			color: var(--white);
		}
	}
	&:active, &.focus, &.hover, &:focus, &:hover, &.focus, &:focus {
		background: var(--dark);
		border-color: var(--dark);
		filter: brightness(1.25);
		text-shadow: none !important;
		color: var(--white);
	}
}
.post-format-icon.post-format-video:before {
	color: #de0e0e;
}
#adminmenu li.wp-has-submenu.wp-not-current-submenu:hover:after {
	right: 40px;
}
#dashboard-widgets-wrap .postbox {
	color: #222;
}
ul#adminmenu {
	a.wp-has-current-submenu:after, >li.current>a.current:after {
		border-right-color: rgba(255, 255, 255, 0);
	}
}
#adminmenu {
	.current div.wp-menu-image:before, .wp-has-current-submenu div.wp-menu-image:before {
		color: var(--dark);
	}
	a {
		&.current:hover div.wp-menu-image:before, &.wp-has-current-submenu:hover div.wp-menu-image:before {
			color: var(--dark);
		}
	}
	li.wp-has-current-submenu {
		a:focus div.wp-menu-image:before, &.opensub div.wp-menu-image:before, &:hover div.wp-menu-image:before {
			color: var(--dark);
		}
	}
	div.wp-menu-image:before {
		color: var(--dark);
	}
}
ul {
	&#destaque-tabs, &#categoria-tabs {
		display: none !important;
	}
}
#destaque-adder, .wp-hidden-children, #edit-slug-box {
	display: none !important;
}
#side-sortables ul {
	margin: 0 !important;
}
.wp-core-ui .notice.is-dismissible {
	padding-right: 38px;
	position: fixed;
	bottom: 0;
	left: auto;
	right: 20px;
	z-index: 999999;
}
#screen-meta-links .show-settings {
	&:after {
		left: 3px;
		bottom: 5px;
		position: absolute;
	}
	color: transparent !important;
	width: 39px;
	overflow: hidden;
	background: #eceef3;
}
#screen-meta {
	background: rgba(220, 221, 222, 0.66);
}
img.attachment-admin-list-thumb.size-admin-list-thumb.wp-post-image {
	height: 70px;
	width: auto;
}
#imagensadicinais h2.hndle.ui-sortable-handle {
	font-size: 18px;
	padding: 3px 12px;
	margin: 0;
	line-height: 1.4;
	color: #000;
	background: var(--white);
	font-weight: 700;
	border-bottom: 0;
	border-top: 1px solid #eceef3;
	border-radius: 0;
}
.no-customize-support #wpadminbar .hide-if-no-customize, .no-customize-support .hide-if-no-customize {
	display: inline-block;
}
#all_views_dashboard_widget, #views_dashboard_widget, #prev_views_dashboard_widget {
	.inside a {
		text-decoration: none;
		white-space: nowrap;
		max-width: 90%;
		overflow: hidden;
		display: inline-block;
		vertical-align: middle;
	}
}
tr.post-3.type-page.status-draft {
	background: #f1e5e0;
}
.postbox-header {
	background: none;
	border-bottom: 1px solid #ccd0d452;
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
	overflow: hidden;
}
.postbox .handle-order-higher, .postbox .handle-order-lower, .toggle-indicator {
	color: var(--dark);
	width: 17px;
}
.postbox .handlediv {
	width: 26px;
}
.postbox.closed h2 {
	background: #f5f5f5 !important;
	color: #333333 !important;
}
.theme-name {
	padding-bottom: 32px !important;
}
/* =================================================
 //
==================================================*/
.wp-core-ui .button.btn, .btn {
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	min-height: 36px;
	padding: 0 20px;
	overflow: hidden;
	font-weight: 500;
	line-height: 1.25px;
	color: var(--white);
	text-align: center;
	text-transform: none;
	vertical-align: middle;
	cursor: pointer;
	background: var(--dark);
	border: 1px solid #2122254d;
	border-radius: 0px;
	outline: 0 !important;
	transition: all 0.35s;
	&:hover {
		background: var(--hover) !important;
		color: var(--white);
	}
}
.wp-core-ui .button-primary-disabled, .wp-core-ui .button-primary.disabled, .wp-core-ui .button-primary:disabled, .wp-core-ui .button-primary[disabled] {
	color: #baadad !important;
	background: #f5f5f5 !important;
	border-color: #dddbdb !important;
}
.bg-primary {
	background: var(--dark);
	&.btn {
		&:hover {
			background: var(--hover);
		}
	}
}
#theme-welcome-panel .btn {
	min-height: 40px;
}
/*=========================================
//		
=========================================*/
body.tools_page_wp-migrate-db {
	.wpmdb .mdb-free-sidebar, .free-disabled, .license-block, .free.wpmdb .header-wrap {
		display: none;
	}
	.wpmdb .btn, wpmdb .btn-stroke {
		background-color: transparent;
		color: var(--dark);
		border-radius: 4px;
		padding: 0 20px;
		border: 1px solid var(--dark);
		&:hover {
			color: #fff
		}
	}
	.action-panel .panel-header-wrap.child-panel.panel-open {
		background: #7f8996
	}
	.wpmdb .nav-wrap {
		margin-left: 34px;
	}
	.wpmdb .wrapper-free {
		display: block
	}
}
body:not(.tools_page_wp-migrate-db) {
	.container-fluid:after, .container-fluid:before, .container:after, .container:before, .inner:after, .inner:before, .row:after, .row:before {
		display: table;
		content: " ";
	}
	.container, .inner, .container-fluid {
		padding-left: 15px;
		padding-right: 15px;
	}
	.clearfix:after, .container-fluid:after, .container:after, .inner:after, .form-horizontal .form-group:after, .row:after {
		clear: both;
	}
	.row:after, .row:before {
		display: table;
		content: " ";
	}
	.row {
		margin-left: -15px;
		margin-right: -15px;
	}
}
legend {
	display: block;
	width: 100%;
	margin-bottom: 20px;
	font-size: 21px;
	line-height: inherit;
	color: var(--dark);
	border-bottom: 1px solid #e5e5e5;
}
label {
	margin-bottom: 5px;
	font-weight: 600;
	line-height: 32px
}
td.wordcount.column-wordcount span {
	font-size: 12px;
	padding: 2px;
	display: block;
	text-align: center;
}
input[type="text"].col75 {
	width: 73%;
}
legend.radio, label.radio {
	display: inline-block;
	vertical-align: middle;
	padding-right: 22px;
}
.menu-settings-group-name {
	font-size: 16px;
	border: 0;
}
.bg-white {
	background: #fff !important;
}
.bg-primary {
	background: var(--dark) !important;
}
.bg-dark {
	background: var(--dark) !important;
}
.bg-gray {
	background: var(--gray) !important;
	&.btn {
		color: #000;
		&:hover {
			color: var(--white);
		}
	}
}
ul.eclass-elements, ol.eclass-elements, div.eclass-elements {
	display: table;
	margin: 0;
}
.eclass-elements-item {
	display: block;
	float: left;
	margin-right: 10px;
}
.eclass-elements-item-link {
	display: block;
}
.eclass-elements-item-description {
	display: block;
	text-align: center;
}
@media (min-width: 1200px) {
	.edit-post-visual-editor .editor-block-list__block, .wp-block {
		width: 95%;
		max-width: 95%;
	}
}
@media (min-width: 782px) {
	body.auto-fold .edit-post-layout__content {
		margin-left: 200px;
	}
}
.upgrade-to-pro {
	display: none !important
}
.misc-pub-section {
	padding: 0 0 5px 10px;
	font-size: 12px;
	line-height: 20px;
}
.filter-items {
	float: none;
	display: inline;
}
.tablenav.top>div, .alignleft.actions.bulkactions {
	display: flex;
}
.wp-core-ui .notice.is-dismissible {
	padding-right: 38px;
	position: fixed;
	top: 35px;
	bottom: auto;
	padding: 10px 22px;
	left: auto;
	right: 20px;
	z-index: 999999;
	animation-delay: 4s;
	animation-name: notice;
	animation-duration: 1.2s;
	animation-fill-mode: forwards;
	.notice-dismiss {
		top: -7px;
		right: -7px;
	}
}
@keyframes notice {
	0% {
		transform: none
	}
	50% {
		transform: none
	}
	90% {
		transform: translateX(80%);
	}
	100% {
		transform: translateX(120%);
		display: none
	}
}
.misc-pub-section {
	padding: 0 0 5px 10px;
	font-size: 12px;
	line-height: 20px;
}
#lokhal_verify_email_popup.lokhal_verify_email_popup, .wrap.wp-filemanager-wrap .r_wfmrs {
	display: none !important;
}
tbody#the-list td {
	border-bottom: 1px solid #ccd0d452;
}
td.tcb_post_thumb.column-tcb_post_thumb {
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center
}
/*=========================================
//		
=========================================*/
.font-11 {
	font-size: 11px;
}
.font-12 {
	font-size: 12px;
}
.font-13 {
	font-size: 13px;
}
.font-14 {
	font-size: 14px;
}
.font-18 {
	font-size: 18px;
}
.font-20 {
	font-size: 20px;
}
.font-22 {
	font-size: 22px;
}
.font-24 {
	font-size: 24px;
}
.font-26 {
	font-size: 26px;
}
.font-28 {
	font-size: 28px !important;
}
.font-36 {
	font-size: 38px;
}
.font-40 {
	font-size: 40px;
}
.font-48 {
	font-size: 48px;
}
.font-60 {
	font-size: 60px;
}
hr {
	height: 1px;
	border: 0;
	background: rgba(136, 136, 136, 0.28);
	margin: 15px 0;
}
/*=========================================
//		
=========================================*/
$paddingMarginValues: ("p": "padding",
	"pt": "padding-top",
	"pb": "padding-bottom",
	"pl": "padding-left",
	"pr": "padding-right",
	"m": "margin",
	"mt": "margin-top",
	"mb": "margin-bottom",
	"ml": "margin-left",
	"mr": "margin-right"
);
/*=========================================
//		
=========================================*/
@for $i from 1 through 6 {
	$value: $i * 5;
	@each $class,
	$property in $paddingMarginValues {
		.#{$class}#{$value} {
			#{$property}: #{$value}px;
		}
	}
}
@for $i from 1 through 10 {
	$value: $i * 10;
	@each $class,
	$property in $paddingMarginValues {
		.#{$class}#{$value} {
			#{$property}: #{$value}px;
		}
	}
}
@for $i from 0 through 0 {
	$value: $i;
	@each $class,
	$property in $paddingMarginValues {
		.#{$class}#{$value} {
			#{$property}: #{$value}px;
		}
	}
}
.col100,
main {
	width: 100%;
}
/*=========================================
//	welcome panel	
=========================================*/
.welcome-panel::before {
	display: none
}
.welcome-panel-column {
	display: block;
}
.welcome-panel h2 {
	color: #000;
	font-size: 26px;
}
.welcome-panel-content {
	min-height: 50px;
}
.welcome-panel .welcome-panel-column-container {
	padding: 0;
}
#theme-welcome-panel {
	margin: 30px 0;
	box-shadow: rgba(55, 55, 89, 0.08) 0px 5px 20px;
	background: var(--white);
	border-radius: 15px;
	padding: 20px;
	border-width: 0px;
}
#theme-welcome-panel .g.cg0 a:first-child {
	border-top-left-radius: 5px !important;
	border-bottom-left-radius: 5px !important;
}
#theme-welcome-panel .g.cg0 a:last-child {
	border-top-right-radius: 5px !important;
	border-bottom-right-radius: 5px !important;
}
/* =================================================
 // include grid
==================================================*/
@import "../components/_grid.scss";
/* ==========================================================================
// metabox
========================================================================== */
.overf .mce-container>div {
	white-space: nowrap;
	overflow: hidden;
}
img[src=""] {
	opacity: 0;
	visibility: hidden;
}
.direction-rtl {
	direction: rtl
}
input#link-video {
	direction: rtl;
}
img#img-intro-img {
	background-color: #f2f2f2;
}
.editor200 textarea.wp-editor-area {
	min-height: 200px;
}
.editor300 textarea.wp-editor-area {
	min-height: 300px;
}
.img80 img {
	height: 80px;
	width: auto
}
.flexible-field {
	margin-bottom: 15px;
	position: relative;
}
.flexible-field label {
	display: block;
	margin-bottom: 5px;
	font-weight: 600;
}
.flexible-field input[type="text"], .flexible-field input[type="url"], .flexible-field input[type="number"], .flexible-field input[type="datetime-local"], .flexible-field textarea, .flexible-field select {
	width: 100%;
	padding: 8px;
	box-sizing: border-box;
}
.flexible-field .radio-label {
	display: inline-block;
	margin-right: 15px;
	font-weight: normal;
}
.media-field {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
.media-field .media-input {
	flex: 1;
	margin-right: 10px;
}
.media-preview {
	margin-top: 10px;
	max-width: 150px;
}
.media-preview img {
	max-width: 100%;
	height: auto;
	border: 1px solid #ddd;
	padding: 2px;
}
.row-separator {
	height: 15px;
	border-bottom: 1px solid #eee;
	margin-bottom: 15px;
}
.field-description {
	color: #666;
	font-style: italic;
	margin: 5px 0 0;
	font-size: 13px;
}
/* ===========================================================
// metabox
=========================================================== */
.grid-meta-default {
	display: grid;
	gap: 10px 10px;
	grid-template-columns: auto 150px 150px 150px 80px;
	margin-bottom: 20px;
}
.repeatable-field {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 5px;
}
.grid-meta-default, .repeatable-field {
	input, select {
		line-height: 34px;
		height: 34px
	}
}
/* ===========================================================
// 
=========================================================== */
.metabox-block {
	background: #fff;
	border: 1px solid #ccd0d4;
	padding: 15px;
	margin-bottom: 10px;
}
.metabox-title {
	width: 100%;
	font-weight: bold;
	font-size: 16px;
	margin-bottom: 10px;
}
.options-list {
	list-style: none;
	margin: 10px 0;
	padding: 0;
}
.option-item {
	margin-bottom: 5px;
	background: #f9f9f9;
	padding: 8px;
	border: 1px solid #ddd;
}
.sortable-placeholder {
	background: #ffe;
	border: 2px dashed #ccc;
	height: 40px;
	margin: 5px 0;
}
.group {
	display: grid;
	align-items: center;
	grid-template-columns: auto 150px 150px 150px 150px;
}
.editor200 textarea {
	height: 200px;
}
.editor300 textarea {
	height: 300px;
}
@media(min-width:768px) {
	.g.grid5 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
		>div {
			grid-column-start: auto;
		}
	}
}