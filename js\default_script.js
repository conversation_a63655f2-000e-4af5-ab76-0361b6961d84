/*=========================================
//		Utility functions
=========================================*/
const el = (elm) => document.querySelectorAll(elm);

/*=========================================
//		Page Optimization
=========================================*/
document.addEventListener('DOMContentLoaded', () => {
	optimizePage();
	addLoadClass();
	handleLazyLoading();
	autoPlayVideos();
	embedYouTubeVideos();
	setupMenu();
	setupFormAsides();
	handleScroll();
	setupShareButtons();
	handleCookieConsent();
});

function optimizePage() {
	if (document.body.classList.contains('admin-bar')) return;

	const windowWidth = window.innerWidth;
	switch (true) {
		case windowWidth >= 992:
			removeElements('.hmd');
			break;
		case windowWidth >= 768 && windowWidth <= 991:
			removeElements('.hsm');
			break;
		case windowWidth <= 767:
			removeElements('.hxs');
			break;
	}
	setImagesDimensions();
}

function removeElements(selector) {
	const elements = el(selector);
	elements.forEach(element => element.remove());
}

function setImagesDimensions() {
	const images = el("img");
	images.forEach(image => {
		if (!image.hasAttribute("width")) {
			image.setAttribute("width", image.parentElement.offsetWidth);
		}
		if (!image.hasAttribute("height")) {
			image.setAttribute("height", image.parentElement.offsetHeight);
		}
	});
}

/*=========================================
//		Add load class
=========================================*/
function addLoadClass() {
	window.onload = () => document.body.classList.add('load');
}

/*=========================================
//		Lazy Loading
=========================================*/
function handleLazyLoading() {
	const cleanerIMG = '&quality=100&ssl=1';
	const preloadOffset = "300px 0px";

	const lazyImages = el("img.src");
	const lazyBg = el(".bg");
	const lazyIframes = el('.iframe');
	const lazyInspect = el('.inspect');

	const lazyLoad = (entries, observer) => {
		entries.forEach(entry => {
			if (entry.isIntersecting && entry.target.getAttribute('data-src')) {
				const img = entry.target;
				const width = img.parentElement.offsetWidth;
				const extension = img.getAttribute('data-src').split('.').pop();
				let datascr = `${img.getAttribute('data-src')}?w=${width}${cleanerIMG}`;
				if (extension === 'jpg' || extension === 'jpeg' || extension === 'png') {
					datascr = datascr.replace("https://", "https://i2.wp.com/");
				}
				img.setAttribute("src", datascr);
				img.removeAttribute('data-src');
				img.classList.remove("src");
				observer.unobserve(img);
			}
		});
	};

	const lazyBgLoad = (entries, observer) => {
		entries.forEach(entry => {
			if (entry.isIntersecting && entry.target.getAttribute('data-bg')) {
				const bg = entry.target;
				const width = bg.offsetWidth;
				const height = bg.offsetHeight;
				const extension = bg.getAttribute('data-bg').split('.').pop();
				let databg = `${bg.getAttribute('data-bg')}?resize=${width},${height}${cleanerIMG}`;
				if (extension === 'jpg' || extension === 'jpeg' || extension === 'png') {
					databg = databg.replace("https://", "https://i2.wp.com/");
				}
				bg.style.backgroundImage = `url(${databg})`;
				bg.removeAttribute('data-bg');
				bg.classList.remove("src");
				observer.unobserve(bg);
			}
		});
	};

	const lazyIframeLoad = (entries, observer) => {
		entries.forEach(entry => {
			if (entry.isIntersecting || entry.intersectionRatio > 0) {
				const lazyIframe = entry.target;
				const src = lazyIframe.getAttribute('data-src');
				lazyIframe.setAttribute("src", src);
				lazyIframe.outerHTML = lazyIframe.outerHTML.replace(/aside/g, "iframe");
				lazyIframe.classList.remove("iframe");
				observer.unobserve(lazyIframe);
			}
		});
	};

	const inspectLoad = (entries, observer) => {
		entries.forEach(entry => {
			if (entry.isIntersecting) {
				const inspectElement = entry.target;
				if (inspectElement.getAttribute('data-delay')) {
					const inspectDelay = inspectElement.getAttribute('data-delay');
					inspectElement.style.transitionDelay = `${inspectDelay}s`;
					inspectElement.removeAttribute('data-delay');
				}
				inspectElement.classList.add("in");
				setTimeout(function(){inspectElement.style.transitionDelay = ""},1000);
				observer.unobserve(inspectElement);
			}
		});
	};

	//const lazyImagesObserver = new IntersectionObserver(lazyLoad);
	//const lazyBgObserver = new IntersectionObserver(lazyBgLoad);
	//const lazyIframeObserver = new IntersectionObserver(lazyIframeLoad);
	//const inspectObserver = new IntersectionObserver(inspectLoad);

	const lazyImagesObserver = new IntersectionObserver(lazyLoad, { rootMargin: preloadOffset });
	const lazyBgObserver = new IntersectionObserver(lazyBgLoad, { rootMargin: preloadOffset });
	const lazyIframeObserver = new IntersectionObserver(lazyIframeLoad, { rootMargin: preloadOffset });
	const inspectObserver = new IntersectionObserver(inspectLoad, { rootMargin: preloadOffset });

	
	lazyImages.forEach(img => lazyImagesObserver.observe(img));
	lazyBg.forEach(bg => lazyBgObserver.observe(bg));
	lazyIframes.forEach(lazyIframe => lazyIframeObserver.observe(lazyIframe));
	lazyInspect.forEach(inspectElement => inspectObserver.observe(inspectElement));
}

/* =================================================
//  Autoplay video
==================================================*/
function autoPlayVideos() {
	const autoplays = Array.from(el('.autoplay'));
	if ("IntersectionObserver" in window) {
		const autoplayObserver = new IntersectionObserver((entries, observer) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					const autoplay = entry.target;
					autoplay.play();
					autoplay.setAttribute("loop", "");
					autoplay.setAttribute("muted", "");
					autoplay.setAttribute("volume", "0");
					autoplay.setAttribute("preload", "auto");
					autoplayObserver.unobserve(autoplay);
				}
			});
		});
		autoplays.forEach(autoplay => autoplayObserver.observe(autoplay));
	}
}

/*=========================================
//		Embed YouTube videos
=========================================*/	
function embedYouTubeVideos() {
	const svgPlay = '<svg viewBox="0 0 68 48" version="1.1" width="80" height="40"><path class="ytp-large-play-button-bg" d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z" fill="#f00"></path><path d="M 45,24 27,14 27,34" fill="#fff"></path></svg>';
	const embedLazy = el('.playtube');

	function toggleIframe() {
		const isActive = this.classList.contains('iframe-active');
		if (isActive) {
			this.innerHTML = `<span class="play">${svgPlay}</span>`;
			this.classList.remove('iframe-active');
		} else {
			const iframe = document.createElement('iframe');
			iframe.setAttribute('src', `https://www.youtube-nocookie.com/embed/${this.getAttribute('data-embed')}?controls=0&amp;showinfo=0&amp;autoplay=1`);
			iframe.setAttribute('allow', 'autoplay;encrypted-media');
			iframe.setAttribute('allowfullscreen', '');
			this.appendChild(iframe);
			this.classList.add('iframe-active');
		}
	}

	embedLazy.forEach(embed => embed.addEventListener('click', toggleIframe));

	const videoContainers = el('.video-container span.play');
	videoContainers.forEach(play => play.innerHTML = svgPlay);
}

/*=========================================
//		Menu setup
=========================================*/
function setupMenu() {
	const openMenu = el('.open-menu');
	openMenu.forEach(element => {
		element.addEventListener('click', function () {
			this.classList.toggle("active");
			document.body.classList.toggle("active-menu");
		});
	});

	const hasChildren = el('.menu-item-has-children');
	hasChildren.forEach(element => {
		element.addEventListener('click', function () {
			this.classList.toggle("active");
		});
	});
}

/*==================================*/
//		Form aside
/*==================================*/
function setupFormAsides() {
	const elements = el('aside.aside');

	const handleClick = function () {
		if (!this.hasAttribute("method")) {
			this.setAttribute("method", "post");
			this.setAttribute("action", "");
		}
		this.outerHTML = this.outerHTML.replace(/aside/g, "form");
	};

	elements.forEach(element => {
		element.addEventListener('click', handleClick);
		element.addEventListener('mouseover', handleClick);
	});
}

/*=========================================
//		Scroll handling
=========================================*/
let lastScrollY = 0;
function handleScroll() {
    window.addEventListener("scroll", () => {
        const body = document.querySelector("body");
        const currentScrollY = window.scrollY;
        if (Math.abs(currentScrollY - lastScrollY) > 140) {
            body.classList.toggle("active", currentScrollY > 140);
            lastScrollY = currentScrollY;
        }
    });
}

/*=========================================
//		Share buttons
=========================================*/	
function setupShareButtons() {
	const shareButtons = el('.share');
	if ('share' in navigator) {
		shareButtons.forEach(shareButton => {
			shareButton.addEventListener('click', () => {
				navigator.share({
					title: shareButton.getAttribute('title'),
					url: shareButton.getAttribute('data-url')
				}).then(() => {
					console.log('Shared');
				}).catch(console.error);
			});
		});
	} else {
		shareButtons.forEach(shareButton => {
			shareButton.setAttribute('disabled', '');
		});
	}
}

/*=========================================
//		Cookie Consent
=========================================*/
function handleCookieConsent() {
	const cookieC = document.getElementById("cookieConsentContainer");
	const cookiesPrivacy = localStorage.getItem("cookiesprivacy");

	if (!cookiesPrivacy && cookieC) {
		const showCookieConsent = () => {
			if (cookieC.classList.contains("hidden")) {
				cookieC.classList.remove("hidden");
				document.removeEventListener("mousemove", showCookieConsent);
				document.removeEventListener("scroll", showCookieConsent);
				document.removeEventListener("click", showCookieConsent);
			}
		};

		document.addEventListener("mousemove", showCookieConsent);
		document.addEventListener("scroll", showCookieConsent);
		document.addEventListener("click", showCookieConsent);
	}
}

/*=========================================
//		
=========================================*/
function scrollToElementWithOffset(elementId, offset) {
	const element = document.getElementById(elementId);
	if (element) {
		const rect = element.getBoundingClientRect();
		const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
		const targetScrollPosition = scrollTop + rect.top - offset;
		window.scrollTo({
			top: targetScrollPosition,
			behavior: 'smooth'
		});
	}
}
document.querySelectorAll('[data-anchor]').forEach(anchor => {
	anchor.addEventListener('click', function(e) {
		e.preventDefault();
		let targetId;
		if (this.getAttribute('data-anchor')) {
			targetId = this.getAttribute('data-anchor');
		} else {
			targetId = this.getAttribute('href').substring(1);
		}
		const offset = parseInt(this.getAttribute('data-ofsset'), 10);
		setTimeout(() => {
			scrollToElementWithOffset(targetId, offset);
		}, 50);
	});
});

/*=========================================
//		form aside
=========================================*/
var elements = document.querySelectorAll('form.aside');
function updateFormAttributes(element) {
	if (!element.hasAttribute("method")) {
		element.setAttribute("method", "post");
	}
	if (!element.hasAttribute("action")) {
		element.setAttribute("action", "");
	}
	const check = element.querySelector('[type="submit"]');
	check.setAttribute('name', "test_form");
	check.setAttribute('value', "none");
	element.classList.remove("aside");
}
for (var i = 0; i < elements.length; i++) {
	elements[i].addEventListener('click', function() {
		updateFormAttributes(this);
	}, false);
	elements[i].addEventListener('mouseover', function() {
		updateFormAttributes(this);
	}, false);
}
/*=========================================
	// Cria e adiciona o link prerender
=========================================*/
if (!document.body.classList.contains('admin-bar')) {
    const links = document.querySelectorAll('header a, main a');
    links.forEach(link => {
        link.addEventListener('mouseover', function () {
            const prefetchLinks = document.querySelectorAll('[rel="prefetch"], [rel="prerender"]');
            prefetchLinks.forEach(existingLink => existingLink.remove());

            const href = this.getAttribute('href');
            if (href) {
                const prefetch = document.createElement('link');
                prefetch.rel = 'prefetch';
                prefetch.as = 'document';
                prefetch.crossOrigin = 'use-credentials';
                prefetch.href = href;
                document.head.appendChild(prefetch);

                const prerender = document.createElement('link');
                prerender.rel = 'prerender';
                prerender.href = href;
                document.head.appendChild(prerender);
            }
        });
    });
}
/* ==========================================================================
// 
========================================================================== */
document.addEventListener("DOMContentLoaded", function () {
    if (window.innerWidth > 768) {
        let scriptAdded = false;

        document.addEventListener("mouseover", function () {
            if (!scriptAdded) {
                scriptAdded = true;
                
                let script = document.createElement("script");
                script.src = T+"/js/smoothscroll.js";
                script.defer = true;

                let footer = document.querySelector("#wp-footer");
                if (footer) {
                    footer.appendChild(script);
                } else {
                    document.body.appendChild(script);
                }
            }
        }, { once: true });
    }
});


/* ===========================================================
// swiper + lazy
=========================================================== */
	function swiperLazyLoad(containerSelector, elementSelector) {
		document.querySelectorAll(`${containerSelector} ${elementSelector}`).forEach(element => {
			const dataBg = element.getAttribute('data-bg');
			if (dataBg) {
				const width = element.offsetWidth;
				const imageUrl = dataBg.replace("https://", "https://i2.wp.com/") + `?w=${width}`;
				if (element.classList.contains('bg-swiper')) {
					element.style.backgroundImage = `url(${imageUrl})`;
					element.classList.add('loaded');
				}
				element.removeAttribute('data-bg');
			}
		});
	}
/* ===========================================================*/
	function loadStylesheet(href) {
		const link = document.createElement("link");
		link.href = href;
		link.rel = "stylesheet";
		link.type = "text/css";
		setTimeout(() => {document.getElementById("wp-footer").appendChild(link);}, 50);
	}

/* ===========================================================*/
const observer = new IntersectionObserver((entries, observerInstance) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      observerInstance.unobserve(entry.target);
      load_swiper();
      //swiperLazyLoad('.swiper', '.bg-swiper');
    }
  });
},{rootMargin: '400px'});
const alvo = document.querySelector('.swiper');
if (alvo) {observer.observe(alvo);}

/* ===========================================================*/
	async function load_swiper() {
		try {
			await import('./swiper.js');
/* ===========================================================*/
			if (document.body.classList.contains('home')) {
				var swiper = new Swiper(".myprefix", {
					slidesPerView: 1,
					spaceBetween: 0,
					speed: 1200,
					loop: true,
					autoplay: {delay: 2500,disableOnInteraction: false,},
					/*pagination: { el: '.myprefix-pagination', clickable: true },*/
					/*navigation: {nextEl: '.myprefix-button-next',prevEl: '.myprefix-button-prev',},*/
					/*breakpoints: {1024: { slidesPerView: 2, spaceBetween: 30 },767: { slidesPerView: 1, spaceBetween: 15 }}*/
				});
			}
/*=========================================================== */
		} catch (error) {
			console.error('Error loading swiper.js:', error);
		}
		loadStylesheet(T + "/css/components/swiper.css");
	}



	if (document.body.classList.contains('home')) {
		
/* ===========================================================
// 
=========================================================== */
(function digitarPalavras() {
  const palavras = ["conectar", "impulsionar", "facilitar"];
  const strongClasses = ["text-primary", "text-secondary", "text-hover"];
  let index = 0;
  const strongEl = document.querySelector("#intro h1 strong");
  if (!strongEl) return;

  function atualizarClasse() {
    strongEl.className = ""; // Remove todas as classes
    strongEl.classList.add(strongClasses[index]); // Adiciona a classe atual
  }

  function digitaTexto(texto, callback) {
    let i = 0;
    strongEl.textContent = "";
    atualizarClasse(); // Atualiza a classe antes de digitar
    const interval = setInterval(() => {
      strongEl.textContent += texto.charAt(i);
      i++;
      if (i >= texto.length) {
        clearInterval(interval);
        if (callback) setTimeout(callback, 1500); // espera antes de apagar
      }
    }, 80);
  }

  function apagarTexto(callback) {
    const texto = strongEl.textContent;
    let i = texto.length;
    const interval = setInterval(() => {
      strongEl.textContent = texto.substring(0, i - 1);
      i--;
      if (i <= 0) {
        clearInterval(interval);
        if (callback) setTimeout(callback, 300);
      }
    }, 40);
  }

  function ciclo() {
    digitaTexto(palavras[index], () => {
      apagarTexto(() => {
        index = (index + 1) % palavras.length;
        ciclo();
      });
    });
  }

  ciclo();
})();

}
