/* ===========================================================
// boxes
=========================================================== */
section#voce_conhece {
	&::before {
		content: "";
		position: absolute;
		left: 0;
		right: 0;
		height: 180px;
		bottom: -12px;
		background: -webkit-linear-gradient(3deg, #000000 20%, #00000080, #00000000);
		background: linear-gradient(3deg, #000000 20%, #00000080, #00000000);
		z-index: 1;
		filter: blur(10px);
	}
}
.box {
	border-radius: 0;
	padding: 18px;
	position: relative;
	background-color: var(--black);
	p:nth-of-type(1) {
		font-size: 24px;
		font-weight: 700;
		//color: var(--secondary);
		line-height: 1.2;
	}
	&::before {
		content: "";
		position: absolute;
		left: -3px;
		right: -3px;
		top: -3px;
		bottom: -3px;
		background: var(--gradient);
		z-index: -1;
	}
}
section#conheca .absolute.bg {
	background-position: bottom center;
}
section#conheca h2 {
	font-size: 34px;
	strong {
		font-size: 60px;
	}
}
.box.g {
	img {
		grid-column-start: span 2;
	}
	p:nth-of-type(1) {
		margin-bottom: 0;
		grid-column-start: span 10;
		font-size: 20px;
		color: var(--secondary);
	}
	p:nth-of-type(2) {
		margin-top: 20px;
	}
}
/* ===========================================================
// section para que serve
=========================================================== */
.before-circle {
	content: "";
	position: absolute;
	left: 50%;
	top: -80px;
	width: 92vw;
	padding-top: 250px;
	filter: blur(80px);
	border-radius: 50%;
	transform: translateX(-50%);
	z-index: 0;
	background: var(--webkit-gradient);
	background: var(--gradient);
}
section#para-que {
	overflow: hidden;
	.absolute.bg {
		background-position: bottom center;
		background-size: 100% auto;
		filter: blur(50px);
		opacity: .75;
	}
}
.para {
	display: grid;
	grid-template-columns: 50px auto;
	column-gap: 20px;
	img {
		width: auto;
		height: 50px;
		filter: -webkit-invert(1);
		filter: invert(1);
	}
	p {
		font-size: 14px;
	}
	p:nth-of-type(1) {
		margin-bottom: 0;
		grid-column-start: span 10;
		font-size: 20px;
		color: var(--secondary);
		font-weight: 600;
	}
}
/* ===========================================================
// plano e valores
=========================================================== */
section#valores .bg {
	background-position: top center;
	background-size: 100% auto;
}
.plano {
	padding: 14px;
	background: -webkit-linear-gradient(180deg, #7a7a7a3d, transparent, #7a7a7a3d);
	background: linear-gradient(180deg, #7a7a7a3d, transparent, #7a7a7a3d);
	//border-left: 1px solid #1a1a1a;
	//border-right: 1px solid #1a1a1a;
	box-shadow: 0px 0px 100px #222 inset;
	position: relative;
	padding-bottom: 132px;
	padding-top: 30px;
	.adquirir-plano {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 20px;
	}
}
.plano-content {
	p.sub-title {
		display: flex;
		min-height: 48px;
	}
	ul {
		margin-bottom: 20px;
	}
	ol {
		margin-bottom: 20px;
	}
	ol li {
		font-size: 14px;
		margin-left: 0;
		border-top: 1px solid var(--dark);
		/* padding-bottom: 4px; */
		/* margin-bottom: 4px; */
		display: flex;
		width: 100%;
		min-height: 56px;
		align-items: center;
	}
}
.btn-plano {
	background: var(--dark);
	border-radius: 2px;
	min-width: 182px;
	&:hover, &:focus {
		background-color: var(--primary);
		color: var(--white);
		filter: none;
	}
}
.valor-anual, .valor-mensal {
	text-align: center;
	font-size: 26px;
	font-weight: 600;
	span {
		font-size: 14px;
		opacity: .5;
		font-weight: 400;
		padding-left: 5px;
	}
}
.valor-anual {
	display: none;
}
.plano-anual {
	#planos-anuais {
		background-color: var(--primary);
		color: var(--white);
	}
	.valor-anual {
		display: block;
	}
	.valor-mensal {
		display: none
	}
}
.plano-mensal {
	#planos-mensais {
		background-color: var(--primary);
		color: var(--white);
	}
}